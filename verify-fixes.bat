@echo off
REM 验证修复效果的脚本

echo ========================================
echo 数据迁移修复效果验证工具
echo ========================================
echo.

echo 正在验证修复效果...
echo.

echo 1. 检查配置文件...
if not exist "src\main\resources\application.properties" (
    echo ❌ 配置文件不存在
    pause
    exit /b 1
)

findstr "elasticsearch.ssl.skip-verification=true" src\main\resources\application.properties >nul
if %errorlevel% equ 0 (
    echo ✅ SSL跳过验证已配置
) else (
    echo ⚠️ SSL跳过验证未配置，可能仍有连接问题
)

echo.
echo 2. 检查项目编译状态...
if not exist "target\classes" (
    echo ⚠️ 项目未编译，正在编译...
    call mvn clean compile -q
    if %errorlevel% neq 0 (
        echo ❌ 编译失败
        pause
        exit /b 1
    )
)
echo ✅ 项目编译正常

echo.
echo 3. 测试日期解析功能...
echo 测试常见的问题日期格式...
java -cp "target\classes;target\dependency\*" com.example.migration.util.DateParsingTester > temp_date_test.log 2>&1
if %errorlevel% equ 0 (
    echo ✅ 日期解析测试通过
    
    REM 检查特定格式的解析结果
    findstr "5/6/2019 12:00:00 AM.*解析成功" temp_date_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ 美式日期格式解析正常
    ) else (
        echo ⚠️ 美式日期格式可能仍有问题
    )
    
    findstr "01/04/1996.*解析成功" temp_date_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ MM/dd/yyyy格式解析正常
    ) else (
        echo ⚠️ MM/dd/yyyy格式可能仍有问题
    )
    
    findstr "1/1/0001.*无效日期" temp_date_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ 无效日期识别正常
    ) else (
        echo ⚠️ 无效日期识别可能有问题
    )
    
) else (
    echo ❌ 日期解析测试失败
    echo 查看详细日志: temp_date_test.log
)

echo.
echo 4. 测试Elasticsearch连接...
java -cp "target\classes;target\dependency\*" com.example.migration.util.ElasticsearchConnectionTester > temp_es_test.log 2>&1
if %errorlevel% equ 0 (
    echo ✅ Elasticsearch连接测试通过
    
    findstr "SSL证书验证" temp_es_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ SSL配置生效
    )
    
) else (
    echo ❌ Elasticsearch连接测试失败
    echo 查看详细日志: temp_es_test.log
    echo.
    echo 可能的原因:
    echo 1. Elasticsearch服务未运行
    echo 2. 网络连接问题
    echo 3. 认证信息错误
)

echo.
echo 5. 检查JAR文件...
if exist "target\mysql-to-elasticsearch-migration-1.0.0.jar" (
    echo ✅ JAR文件存在
) else (
    echo ⚠️ JAR文件不存在，正在打包...
    call mvn package -DskipTests -q
    if %errorlevel% equ 0 (
        echo ✅ JAR文件打包成功
    ) else (
        echo ❌ JAR文件打包失败
    )
)

echo.
echo ========================================
echo 验证结果汇总
echo ========================================

echo 修复状态检查:
findstr "elasticsearch.ssl.skip-verification=true" src\main\resources\application.properties >nul
if %errorlevel% equ 0 (
    echo ✅ SSL连接问题: 已修复
) else (
    echo ❌ SSL连接问题: 未修复
)

if exist "temp_date_test.log" (
    findstr "解析成功" temp_date_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ 日期解析问题: 已修复
    ) else (
        echo ❌ 日期解析问题: 未修复
    )
) else (
    echo ❌ 日期解析问题: 测试未运行
)

if exist "temp_es_test.log" (
    findstr "连接测试成功" temp_es_test.log >nul
    if %errorlevel% equ 0 (
        echo ✅ Elasticsearch连接: 正常
    ) else (
        echo ❌ Elasticsearch连接: 异常
    )
) else (
    echo ❌ Elasticsearch连接: 测试未运行
)

echo.
echo 建议的下一步操作:
echo 1. 如果所有测试都通过，可以运行: run.bat
echo 2. 如果仍有问题，可以运行: fix-all-issues.bat
echo 3. 查看详细文档: PROBLEM_SOLUTIONS.md

echo.
REM 清理临时文件
if exist "temp_date_test.log" del temp_date_test.log
if exist "temp_es_test.log" del temp_es_test.log

pause
