@echo off
REM MySQL到Elasticsearch数据迁移工具启动脚本 (Windows)
REM 使用方法：双击运行或在命令行中执行 run.bat

echo ========================================
echo MySQL到Elasticsearch数据迁移工具
echo 版本: 1.0.0
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java运行环境
    echo 请确保已安装Java 8或更高版本，并配置了JAVA_HOME环境变量
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "application.properties" (
    echo 警告: 未找到配置文件 application.properties
    if exist "application.properties.example" (
        echo 发现示例配置文件，正在复制...
        copy "application.properties.example" "application.properties"
        echo.
        echo 请编辑 application.properties 文件，配置数据库连接信息
        echo 配置完成后重新运行此脚本
        pause
        exit /b 1
    ) else (
        echo 错误: 未找到配置文件，请创建 application.properties
        pause
        exit /b 1
    )
)

REM 检查JAR文件
set JAR_FILE=target\mysql-to-elasticsearch-migration-1.0.0.jar
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件 %JAR_FILE%
    echo 请先运行 mvn clean package 编译项目
    pause
    exit /b 1
)

REM 创建日志目录
if not exist "logs" mkdir logs

REM 设置JVM参数
set JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication

echo 正在测试Elasticsearch连接...
java -cp "target\classes;target\dependency\*" com.example.migration.util.ElasticsearchConnectionTester
if %errorlevel% neq 0 (
    echo.
    echo ❌ Elasticsearch连接测试失败
    echo 请运行 quick-fix.bat 解决连接问题
    pause
    exit /b 1
)

echo.
echo ✅ Elasticsearch连接测试成功
echo 正在启动数据迁移工具...
echo JAR文件: %JAR_FILE%
echo JVM参数: %JAVA_OPTS%
echo.

REM 启动应用程序
java %JAVA_OPTS% -jar "%JAR_FILE%"

REM 检查执行结果
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 数据迁移成功完成！
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 数据迁移失败，错误代码: %errorlevel%
    echo 请检查日志文件获取详细错误信息
    echo ========================================
)

echo.
echo 按任意键退出...
pause >nul
