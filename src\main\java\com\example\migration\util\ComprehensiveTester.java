package com.example.migration.util;

import com.example.migration.config.MigrationConfig;
import com.example.migration.database.DatabaseManager;
import com.example.migration.elasticsearch.ElasticsearchManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
// 移除了LocalDate导入，因为不再处理日期字段
import java.util.ArrayList;
import java.util.List;

/**
 * 综合测试工具
 * 测试日期解析和Elasticsearch连接
 */
public class ComprehensiveTester {
    
    private static final Logger logger = LoggerFactory.getLogger(ComprehensiveTester.class);
    
    public static void main(String[] args) {
        logger.info("=== 数据迁移综合测试工具 ===");
        
        try {
            // 加载配置
            MigrationConfig config = new MigrationConfig();
            config.printConfig();
            
            // 测试1: Elasticsearch连接
            logger.info("\n=== 测试1: Elasticsearch连接 ===");
            testElasticsearchConnection(config);

            // 测试2: MySQL连接和数据采样
            logger.info("\n=== 测试2: MySQL连接和数据采样 ===");
            testMysqlConnectionAndSampling(config);
            
            logger.info("\n=== 综合测试完成 ===");
            
        } catch (Exception e) {
            logger.error("综合测试失败", e);
            System.exit(1);
        }
    }
    
    // 注意：已移除日期解析测试，因为不再处理日期字段
    
    /**
     * 测试Elasticsearch连接
     */
    private static void testElasticsearchConnection(MigrationConfig config) {
        logger.info("开始测试Elasticsearch连接...");
        
        ElasticsearchManager esManager = null;
        try {
            esManager = new ElasticsearchManager(config);
            
            // 测试索引操作
            String indexName = config.getElasticsearchIndexName();
            
            logger.info("测试索引是否存在: {}", indexName);
            boolean exists = esManager.indexExists(indexName);
            logger.info("索引存在状态: {}", exists);
            
            if (!exists) {
                logger.info("尝试创建索引...");
                boolean created = esManager.createIndex(indexName);
                logger.info("索引创建结果: {}", created ? "成功" : "失败");
            }
            
            logger.info("✅ Elasticsearch连接测试成功");
            
        } catch (Exception e) {
            logger.error("❌ Elasticsearch连接测试失败", e);
            
            // 提供解决建议
            if (e.getMessage().contains("SSLHandshakeException")) {
                logger.info("🔧 SSL连接问题解决建议:");
                logger.info("1. 确认配置文件中 elasticsearch.ssl.skip-verification=true");
                logger.info("2. 或者改用HTTP连接: elasticsearch.scheme=http");
                logger.info("3. 运行 quick-fix.bat 或 quick-fix.sh 进行自动修复");
            }
        } finally {
            if (esManager != null) {
                esManager.close();
            }
        }
    }
    
    /**
     * 测试MySQL连接和数据采样
     */
    private static void testMysqlConnectionAndSampling(MigrationConfig config) {
        logger.info("开始测试MySQL连接和数据采样...");
        
        DatabaseManager dbManager = null;
        try {
            dbManager = new DatabaseManager(config);
            
            // 测试连接
            logger.info("测试数据库连接...");
            try (Connection conn = dbManager.getConnection()) {
                logger.info("✅ 数据库连接成功");
            }
            
            // 验证分表
            logger.info("验证分表结构...");
            dbManager.validateTables();
            
            // 采样测试日期数据
            logger.info("采样测试日期数据...");
            sampleDateData(dbManager, config);
            
            logger.info("✅ MySQL连接和数据采样测试成功");
            
        } catch (Exception e) {
            logger.error("❌ MySQL连接测试失败", e);
            
            // 提供解决建议
            logger.info("🔧 MySQL连接问题解决建议:");
            logger.info("1. 检查数据库服务是否运行");
            logger.info("2. 验证连接配置（主机、端口、用户名、密码）");
            logger.info("3. 确认数据库和表是否存在");
        } finally {
            if (dbManager != null) {
                dbManager.close();
            }
        }
    }
    
    /**
     * 采样日期数据进行测试
     */
    private static void sampleDateData(DatabaseManager dbManager, MigrationConfig config) {
        String tablePrefix = config.getTablePrefix();
        int sampleSize = 10; // 每个表采样10条记录
        
        logger.info("从前3张表采样日期数据进行测试...");
        
        for (int i = 0; i < Math.min(3, config.getTableCount()); i++) {
            String tableName = tablePrefix + i;
            
            if (!dbManager.tableExists(tableName)) {
                logger.warn("表 {} 不存在，跳过", tableName);
                continue;
            }
            
            logger.info("采样表: {}", tableName);

            String sql = "SELECT uid, phone_number, first_name, last_name, email FROM " + tableName + " LIMIT ?";

            try (Connection conn = dbManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setInt(1, sampleSize);

                try (ResultSet rs = stmt.executeQuery()) {
                    int recordCount = 0;
                    List<String> uids = new ArrayList<>();
                    List<String> phoneNumbers = new ArrayList<>();
                    List<String> emails = new ArrayList<>();

                    while (rs.next()) {
                        recordCount++;
                        String uid = rs.getString("uid");
                        String phoneNumber = rs.getString("phone_number");
                        String email = rs.getString("email");

                        if (uid != null && !uid.trim().isEmpty()) uids.add(uid);
                        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) phoneNumbers.add(phoneNumber);
                        if (email != null && !email.trim().isEmpty()) emails.add(email);
                    }

                    logger.info("表 {} 采样结果:", tableName);
                    logger.info("  总记录数: {}", recordCount);
                    logger.info("  有效UID数: {}", uids.size());
                    logger.info("  有效手机号数: {}", phoneNumbers.size());
                    logger.info("  有效邮箱数: {}", emails.size());
                }
                
            } catch (Exception e) {
                logger.error("采样表 {} 时发生错误", tableName, e);
            }
        }
    }
    
    // 注意：已移除日期测试方法，因为不再处理日期字段
}
