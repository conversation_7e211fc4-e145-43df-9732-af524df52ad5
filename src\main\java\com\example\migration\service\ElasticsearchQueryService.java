package com.example.migration.service;

import com.example.migration.config.MigrationConfig;
import com.example.migration.elasticsearch.ElasticsearchManager;
import com.example.migration.model.QueryResult;
import com.example.migration.model.UserData;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Elasticsearch查询服务类
 * 提供各种查询功能
 */
public class ElasticsearchQueryService {
    
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchQueryService.class);
    
    private ElasticsearchManager elasticsearchManager;
    private MigrationConfig config;
    private ObjectMapper objectMapper;
    private String indexName;
    
    public ElasticsearchQueryService(MigrationConfig config) {
        this.config = config;
        this.elasticsearchManager = new ElasticsearchManager(config);
        this.indexName = config.getElasticsearchIndexName();
        
        // 配置JSON序列化器
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }
    
    /**
     * 根据UID精确查询用户信息
     */
    public QueryResult findByUid(String uid) {
        logger.info("根据UID查询用户: {}", uid);
        
        QueryBuilder query = QueryBuilders.termQuery("uid", uid);
        String description = "UID精确查询: " + uid;
        
        return executeQuery(query, description, 1, 10, false);
    }
    
    /**
     * 根据手机号查询用户
     */
    public QueryResult findByPhoneNumber(String phoneNumber) {
        logger.info("根据手机号查询用户: {}", phoneNumber);
        
        QueryBuilder query = QueryBuilders.termQuery("phone_number", phoneNumber);
        String description = "手机号精确查询: " + phoneNumber;
        
        return executeQuery(query, description, 1, 10, false);
    }
    
    /**
     * 根据姓名模糊查询
     */
    public QueryResult findByName(String name, int page, int size) {
        logger.info("根据姓名模糊查询: {}, 页码: {}, 大小: {}", name, page, size);
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        // 在多个姓名字段中搜索
        boolQuery.should(QueryBuilders.matchQuery("first_name", name).boost(2.0f))
                .should(QueryBuilders.matchQuery("last_name", name).boost(2.0f))
                .should(QueryBuilders.matchQuery("full_name", name).boost(3.0f))
                .should(QueryBuilders.wildcardQuery("full_name.keyword", "*" + name + "*"))
                .minimumShouldMatch(1);
        
        String description = "姓名模糊查询: " + name;
        
        return executeQuery(boolQuery, description, page, size, true);
    }
    
    /**
     * 根据工作地点查询
     */
    public QueryResult findByWorkplace(String workplace, int page, int size) {
        logger.info("根据工作地点查询: {}, 页码: {}, 大小: {}", workplace, page, size);
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        // 在工作地点相关字段中搜索
        boolQuery.should(QueryBuilders.matchQuery("workplace_city", workplace).boost(2.0f))
                .should(QueryBuilders.matchQuery("work_place", workplace).boost(1.5f))
                .should(QueryBuilders.wildcardQuery("workplace_city", "*" + workplace + "*"))
                .should(QueryBuilders.wildcardQuery("work_place.keyword", "*" + workplace + "*"))
                .minimumShouldMatch(1);
        
        String description = "工作地点查询: " + workplace;
        
        return executeQuery(boolQuery, description, page, size, true);
    }
    
    /**
     * 复合条件查询
     */
    public QueryResult findByMultipleConditions(Map<String, String> conditions, int page, int size) {
        logger.info("复合条件查询: {}, 页码: {}, 大小: {}", conditions, page, size);
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        List<String> conditionDescriptions = new ArrayList<>();
        
        for (Map.Entry<String, String> entry : conditions.entrySet()) {
            String field = entry.getKey();
            String value = entry.getValue();
            
            if (value == null || value.trim().isEmpty()) {
                continue;
            }
            
            switch (field.toLowerCase()) {
                case "uid":
                    boolQuery.must(QueryBuilders.termQuery("uid", value));
                    conditionDescriptions.add("UID=" + value);
                    break;
                case "phone_number":
                    boolQuery.must(QueryBuilders.termQuery("phone_number", value));
                    conditionDescriptions.add("手机号=" + value);
                    break;
                case "name":
                    BoolQueryBuilder nameQuery = QueryBuilders.boolQuery()
                            .should(QueryBuilders.matchQuery("first_name", value))
                            .should(QueryBuilders.matchQuery("last_name", value))
                            .should(QueryBuilders.matchQuery("full_name", value))
                            .minimumShouldMatch(1);
                    boolQuery.must(nameQuery);
                    conditionDescriptions.add("姓名包含=" + value);
                    break;
                case "gender":
                    boolQuery.must(QueryBuilders.termQuery("gender", value));
                    conditionDescriptions.add("性别=" + value);
                    break;
                case "workplace_city":
                    boolQuery.must(QueryBuilders.matchQuery("workplace_city", value));
                    conditionDescriptions.add("工作城市=" + value);
                    break;
                case "work_place":
                    boolQuery.must(QueryBuilders.matchQuery("work_place", value));
                    conditionDescriptions.add("工作地点包含=" + value);
                    break;
                case "email":
                    boolQuery.must(QueryBuilders.termQuery("email", value));
                    conditionDescriptions.add("邮箱=" + value);
                    break;
                default:
                    logger.warn("不支持的查询字段: {}", field);
                    break;
            }
        }
        
        if (!boolQuery.hasClauses()) {
            // 如果没有有效的查询条件，返回空结果
            QueryResult emptyResult = new QueryResult();
            emptyResult.setData(new ArrayList<>());
            emptyResult.setTotalHits(0);
            emptyResult.setQueryDescription("无有效查询条件");
            return emptyResult;
        }
        
        String description = "复合条件查询: " + String.join(", ", conditionDescriptions);
        
        return executeQuery(boolQuery, description, page, size, true);
    }
    
    /**
     * 全文搜索
     */
    public QueryResult searchAll(String keyword, int page, int size) {
        logger.info("全文搜索: {}, 页码: {}, 大小: {}", keyword, page, size);
        
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        // 在所有文本字段中搜索
        boolQuery.should(QueryBuilders.matchQuery("uid", keyword).boost(3.0f))
                .should(QueryBuilders.matchQuery("phone_number", keyword).boost(3.0f))
                .should(QueryBuilders.matchQuery("first_name", keyword).boost(2.0f))
                .should(QueryBuilders.matchQuery("last_name", keyword).boost(2.0f))
                .should(QueryBuilders.matchQuery("full_name", keyword).boost(2.5f))
                .should(QueryBuilders.matchQuery("email", keyword).boost(2.0f))
                .should(QueryBuilders.matchQuery("workplace_city", keyword).boost(1.5f))
                .should(QueryBuilders.matchQuery("work_place", keyword).boost(1.5f))
                .should(QueryBuilders.matchQuery("birth_place", keyword).boost(1.0f))
                .minimumShouldMatch(1);
        
        String description = "全文搜索: " + keyword;
        
        return executeQuery(boolQuery, description, page, size, true);
    }
    
    /**
     * 执行查询
     */
    private QueryResult executeQuery(QueryBuilder query, String description, int page, int size, boolean enableHighlight) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            
            // 设置查询
            searchSourceBuilder.query(query);
            
            // 设置分页
            int from = (page - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            
            // 设置超时
            searchSourceBuilder.timeout(new org.elasticsearch.core.TimeValue(30, TimeUnit.SECONDS));
            
            // 设置高亮
            if (enableHighlight) {
                HighlightBuilder highlightBuilder = new HighlightBuilder();
                highlightBuilder.field("first_name");
                highlightBuilder.field("last_name");
                highlightBuilder.field("full_name");
                highlightBuilder.field("workplace_city");
                highlightBuilder.field("work_place");
                highlightBuilder.field("email");
                highlightBuilder.preTags("<em>");
                highlightBuilder.postTags("</em>");
                searchSourceBuilder.highlighter(highlightBuilder);
            }
            
            searchRequest.source(searchSourceBuilder);
            
            // 执行查询
            RestHighLevelClient client = elasticsearchManager.getClient();
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            
            // 解析结果
            return parseSearchResponse(searchResponse, description, page, size);
            
        } catch (IOException e) {
            logger.error("执行查询时发生错误: {}", description, e);
            throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析搜索响应
     */
    private QueryResult parseSearchResponse(SearchResponse searchResponse, String description, int page, int size) {
        QueryResult result = new QueryResult();
        
        // 设置基本信息
        result.setTotalHits(searchResponse.getHits().getTotalHits().value);
        result.setTookInMillis(searchResponse.getTook().millis());
        result.setTimedOut(searchResponse.isTimedOut());
        result.setCurrentPage(page);
        result.setPageSize(size);
        result.setQueryDescription(description);
        result.calculateTotalPages();
        
        // 解析文档
        List<UserData> userDataList = new ArrayList<>();
        Map<String, Map<String, List<String>>> highlights = new HashMap<>();
        
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            try {
                // 解析用户数据
                UserData userData = objectMapper.readValue(hit.getSourceAsString(), UserData.class);
                userDataList.add(userData);
                
                // 解析高亮信息
                Map<String, HighlightField> highlightFields = hit.getHighlightFields();
                if (!highlightFields.isEmpty()) {
                    Map<String, List<String>> docHighlights = new HashMap<>();
                    for (Map.Entry<String, HighlightField> entry : highlightFields.entrySet()) {
                        List<String> fragments = new ArrayList<>();
                        for (org.elasticsearch.common.text.Text fragment : entry.getValue().getFragments()) {
                            fragments.add(fragment.string());
                        }
                        docHighlights.put(entry.getKey(), fragments);
                    }
                    highlights.put(hit.getId(), docHighlights);
                }
                
            } catch (Exception e) {
                logger.error("解析搜索结果时发生错误: {}", hit.getId(), e);
            }
        }
        
        result.setData(userDataList);
        result.setHighlights(highlights);
        
        logger.info("查询完成: {}, 总数: {}, 耗时: {}ms", description, result.getTotalHits(), result.getTookInMillis());
        
        return result;
    }
    
    /**
     * 关闭服务
     */
    public void close() {
        if (elasticsearchManager != null) {
            elasticsearchManager.close();
        }
    }
}
