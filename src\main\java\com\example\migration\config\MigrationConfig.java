package com.example.migration.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 迁移配置管理类
 * 负责加载和管理应用程序配置
 */
public class MigrationConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationConfig.class);
    private static final String CONFIG_FILE = "application.properties";
    
    private Properties properties;
    
    public MigrationConfig() {
        loadProperties();
    }
    
    /**
     * 加载配置文件
     */
    private void loadProperties() {
        properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                throw new RuntimeException("无法找到配置文件: " + CONFIG_FILE);
            }
            properties.load(inputStream);
            logger.info("成功加载配置文件: {}", CONFIG_FILE);
        } catch (IOException e) {
            logger.error("加载配置文件失败: {}", CONFIG_FILE, e);
            throw new RuntimeException("加载配置文件失败", e);
        }
    }
    
    /**
     * 获取配置值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取配置值，如果不存在则返回默认值
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整数配置值
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            logger.warn("配置项 {} 的值 {} 不是有效的整数，使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔配置值
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value.trim());
    }
    
    // MySQL配置
    public String getMysqlHost() {
        return getProperty("mysql.host", "localhost");
    }
    
    public int getMysqlPort() {
        return getIntProperty("mysql.port", 3306);
    }
    
    public String getMysqlDatabase() {
        return getProperty("mysql.database");
    }
    
    public String getMysqlUsername() {
        return getProperty("mysql.username");
    }
    
    public String getMysqlPassword() {
        return getProperty("mysql.password");
    }
    
    public String getMysqlDriver() {
        return getProperty("mysql.driver", "com.mysql.cj.jdbc.Driver");
    }
    
    public String getMysqlUrl() {
        return String.format("***********************************************************************************************************************************",
                getMysqlHost(), getMysqlPort(), getMysqlDatabase());
    }
    
    // 连接池配置
    public int getMaximumPoolSize() {
        return getIntProperty("mysql.pool.maximum-pool-size", 20);
    }
    
    public int getMinimumIdle() {
        return getIntProperty("mysql.pool.minimum-idle", 5);
    }
    
    public long getConnectionTimeout() {
        return getIntProperty("mysql.pool.connection-timeout", 30000);
    }
    
    public long getIdleTimeout() {
        return getIntProperty("mysql.pool.idle-timeout", 600000);
    }
    
    public long getMaxLifetime() {
        return getIntProperty("mysql.pool.max-lifetime", 1800000);
    }
    
    // Elasticsearch配置
    public String getElasticsearchHost() {
        return getProperty("elasticsearch.host", "localhost");
    }
    
    public int getElasticsearchPort() {
        return getIntProperty("elasticsearch.port", 9200);
    }
    
    public String getElasticsearchScheme() {
        return getProperty("elasticsearch.scheme", "http");
    }
    
    public String getElasticsearchUsername() {
        return getProperty("elasticsearch.username");
    }
    
    public String getElasticsearchPassword() {
        return getProperty("elasticsearch.password");
    }
    
    public String getElasticsearchIndexName() {
        return getProperty("elasticsearch.index.name", "user_data");
    }
    
    // 迁移配置
    public int getBatchSize() {
        return getIntProperty("migration.batch.size", 1000);
    }
    
    public int getThreadPoolSize() {
        return getIntProperty("migration.thread.pool.size", 10);
    }
    
    public int getTableCount() {
        return getIntProperty("migration.table.count", 1);
    }
    
    public String getTablePrefix() {
        return getProperty("migration.table.prefix", "data_");
    }
    
    /**
     * 验证必要的配置项
     */
    public void validateConfig() {
        StringBuilder errors = new StringBuilder();
        
        if (getMysqlDatabase() == null || getMysqlDatabase().trim().isEmpty()) {
            errors.append("MySQL数据库名称不能为空\n");
        }
        
        if (getMysqlUsername() == null || getMysqlUsername().trim().isEmpty()) {
            errors.append("MySQL用户名不能为空\n");
        }
        
        if (getMysqlPassword() == null) {
            errors.append("MySQL密码不能为空\n");
        }
        
        if (errors.length() > 0) {
            throw new RuntimeException("配置验证失败:\n" + errors.toString());
        }
        
        logger.info("配置验证通过");
    }
    
    /**
     * 打印配置信息（隐藏敏感信息）
     */
    public void printConfig() {
        logger.info("=== 迁移配置信息 ===");
        logger.info("MySQL地址: {}:{}", getMysqlHost(), getMysqlPort());
        logger.info("MySQL数据库: {}", getMysqlDatabase());
        logger.info("MySQL用户名: {}", getMysqlUsername());
        logger.info("连接池最大连接数: {}", getMaximumPoolSize());
        logger.info("Elasticsearch地址: {}://{}:{}", getElasticsearchScheme(), getElasticsearchHost(), getElasticsearchPort());
        logger.info("Elasticsearch索引: {}", getElasticsearchIndexName());
        logger.info("批处理大小: {}", getBatchSize());
        logger.info("线程池大小: {}", getThreadPoolSize());
        logger.info("分表数量: {}", getTableCount());
        logger.info("表名前缀: {}", getTablePrefix());
        logger.info("==================");
    }
}
