package com.example.migration.model;

import java.util.List;
import java.util.Map;

/**
 * 查询结果包装类
 * 包含查询数据、统计信息和高亮信息
 */
public class QueryResult {
    
    /**
     * 查询到的用户数据列表
     */
    private List<UserData> data;
    
    /**
     * 查询总数
     */
    private long totalHits;
    
    /**
     * 查询耗时（毫秒）
     */
    private long tookInMillis;
    
    /**
     * 是否超时
     */
    private boolean timedOut;
    
    /**
     * 当前页码（从1开始）
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 高亮信息
     * Key: 文档ID, Value: 字段名 -> 高亮片段列表
     */
    private Map<String, Map<String, List<String>>> highlights;
    
    /**
     * 查询条件描述
     */
    private String queryDescription;
    
    // 构造函数
    public QueryResult() {}
    
    public QueryResult(List<UserData> data, long totalHits, long tookInMillis) {
        this.data = data;
        this.totalHits = totalHits;
        this.tookInMillis = tookInMillis;
        this.timedOut = false;
    }
    
    // Getter和Setter方法
    public List<UserData> getData() {
        return data;
    }
    
    public void setData(List<UserData> data) {
        this.data = data;
    }
    
    public long getTotalHits() {
        return totalHits;
    }
    
    public void setTotalHits(long totalHits) {
        this.totalHits = totalHits;
    }
    
    public long getTookInMillis() {
        return tookInMillis;
    }
    
    public void setTookInMillis(long tookInMillis) {
        this.tookInMillis = tookInMillis;
    }
    
    public boolean isTimedOut() {
        return timedOut;
    }
    
    public void setTimedOut(boolean timedOut) {
        this.timedOut = timedOut;
    }
    
    public int getCurrentPage() {
        return currentPage;
    }
    
    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }
    
    public Map<String, Map<String, List<String>>> getHighlights() {
        return highlights;
    }
    
    public void setHighlights(Map<String, Map<String, List<String>>> highlights) {
        this.highlights = highlights;
    }
    
    public String getQueryDescription() {
        return queryDescription;
    }
    
    public void setQueryDescription(String queryDescription) {
        this.queryDescription = queryDescription;
    }
    
    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) totalHits / pageSize);
        } else {
            this.totalPages = 1;
        }
    }
    
    /**
     * 是否有下一页
     */
    public boolean hasNextPage() {
        return currentPage < totalPages;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPreviousPage() {
        return currentPage > 1;
    }
    
    /**
     * 获取当前页的起始记录号
     */
    public long getStartRecord() {
        return (long) (currentPage - 1) * pageSize + 1;
    }
    
    /**
     * 获取当前页的结束记录号
     */
    public long getEndRecord() {
        long end = (long) currentPage * pageSize;
        return Math.min(end, totalHits);
    }
    
    /**
     * 是否为空结果
     */
    public boolean isEmpty() {
        return data == null || data.isEmpty();
    }
    
    /**
     * 获取结果数量
     */
    public int getResultCount() {
        return data != null ? data.size() : 0;
    }
    
    @Override
    public String toString() {
        return "QueryResult{" +
                "totalHits=" + totalHits +
                ", tookInMillis=" + tookInMillis +
                ", timedOut=" + timedOut +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                ", totalPages=" + totalPages +
                ", resultCount=" + getResultCount() +
                ", queryDescription='" + queryDescription + '\'' +
                '}';
    }
}
