# MySQL到Elasticsearch数据迁移工具

这是一个专门用于将MySQL分库分表数据迁移到Elasticsearch的Java工具。支持200张分表的并发迁移，具有完善的错误处理和进度监控功能。

## 功能特性

- ✅ 支持MySQL分库分表数据迁移（200张表）
- ✅ 多线程并发处理，提高迁移效率
- ✅ 批量处理，避免内存溢出
- ✅ 完善的错误处理和重试机制
- ✅ 实时进度监控和统计
- ✅ 灵活的配置管理
- ✅ 详细的日志记录
- ✅ 自动创建Elasticsearch索引和映射
- ✅ 支持多种日期格式解析
- ✅ 数据完整性验证

## 系统要求

- Java 8 或更高版本
- MySQL 5.7+ 或 8.0+
- Elasticsearch 7.x
- 至少 2GB 可用内存
- 足够的磁盘空间存储日志文件

## 快速开始

### 🚀 一键修复和运行（推荐）

如果遇到SSL连接或日期解析问题，直接运行一键修复脚本：

```bash
# Windows
fix-all-issues.bat

# Linux/Mac
chmod +x fix-all-issues.sh && ./fix-all-issues.sh
```

### 📋 手动配置步骤

#### 1. 配置数据库连接

编辑 `src/main/resources/application.properties` 文件：

```properties
# MySQL数据库配置
mysql.host=localhost
mysql.port=3306
mysql.database=your_database_name
mysql.username=your_username
mysql.password=your_password

# Elasticsearch配置
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=your_password
elasticsearch.index.name=user_data

# SSL配置（解决证书问题）
elasticsearch.ssl.skip-verification=true

# 迁移配置
migration.batch.size=1000
migration.thread.pool.size=10
migration.table.count=200
migration.table.prefix=data_
```

#### 2. 编译项目

```bash
mvn clean compile
```

#### 3. 运行测试工具

```bash
# 测试日期解析功能
java -cp "target/classes:target/dependency/*" com.example.migration.util.DateParsingTester

# 测试Elasticsearch连接
java -cp "target/classes:target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester

# 综合测试
java -cp "target/classes:target/dependency/*" com.example.migration.util.ComprehensiveTester
```

#### 4. 打包应用

```bash
mvn clean package
```

#### 5. 运行迁移

```bash
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar
```

## 配置说明

### MySQL配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| mysql.host | MySQL服务器地址 | localhost | 是 |
| mysql.port | MySQL服务器端口 | 3306 | 否 |
| mysql.database | 数据库名称 | - | 是 |
| mysql.username | 用户名 | - | 是 |
| mysql.password | 密码 | - | 是 |

### Elasticsearch配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| elasticsearch.host | ES服务器地址 | localhost | 否 |
| elasticsearch.port | ES服务器端口 | 9200 | 否 |
| elasticsearch.scheme | 协议 | http | 否 |
| elasticsearch.username | 用户名（如需认证） | - | 否 |
| elasticsearch.password | 密码（如需认证） | - | 否 |
| elasticsearch.index.name | 目标索引名称 | user_data | 否 |

### 迁移配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| migration.batch.size | 批处理大小 | 1000 | 否 |
| migration.thread.pool.size | 线程池大小 | 10 | 否 |
| migration.table.count | 分表数量 | 200 | 否 |
| migration.table.prefix | 表名前缀 | data_ | 否 |

## 数据映射

### MySQL表结构
```sql
CREATE TABLE data_0 (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone_number VARCHAR(32),
    uid VARCHAR(64),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    gender VARCHAR(10),
    birth_place VARCHAR(100),
    workplace_city VARCHAR(50),
    marital_status VARCHAR(20),
    work_place TEXT,
    join_date VARCHAR(20),
    email VARCHAR(100),
    birth_date VARCHAR(20)
);
```

### Elasticsearch索引映射

工具会自动创建包含以下字段的索引：

- `mysql_id`: 原始MySQL记录ID
- `mysql_table`: 原始MySQL表名
- `uid`: 用户唯一标识
- `phone_number`: 手机号码
- `first_name`: 名字
- `last_name`: 姓氏
- `full_name`: 全名（自动生成）
- `name_initials`: 姓名首字母（自动生成）
- `gender`: 性别
- `birth_date`: 出生日期
- `birth_place`: 出生地
- `workplace_city`: 工作城市
- `marital_status`: 婚姻状况
- `work_place`: 工作地点
- `join_date`: 入职日期
- `email`: 邮箱

## 文档ID规则

Elasticsearch文档ID格式：`{表名}_{MySQL记录ID}`

例如：
- 表 `data_0` 中ID为 `123456` 的记录 → ES文档ID为 `data_0_123456`
- 表 `data_99` 中ID为 `789012` 的记录 → ES文档ID为 `data_99_789012`

## 日志文件

程序运行时会在 `logs/` 目录下生成以下日志文件：

- `migration.log`: 完整的运行日志
- `migration-error.log`: 仅包含错误信息的日志

## 性能优化建议

1. **调整批处理大小**：根据服务器性能调整 `migration.batch.size`
2. **优化线程池大小**：根据CPU核心数调整 `migration.thread.pool.size`
3. **数据库连接池**：根据并发需求调整连接池配置
4. **JVM参数**：为大数据量迁移设置合适的堆内存

```bash
java -Xms2g -Xmx4g -jar migration-tool.jar
```

## 故障排除

### 常见问题

1. **SSL证书验证失败**
   ```
   SSLHandshakeException: PKIX path building failed
   ```
   **解决方案**：
   - 开发环境：设置 `elasticsearch.ssl.skip-verification=true`
   - 生产环境：配置正确的SSL证书路径
   - 详见：[SSL配置指南](SSL_CONFIGURATION_GUIDE.md)

2. **Log4j2日志实现缺失**
   ```
   Log4j2 could not find a logging implementation
   ```
   **解决方案**：项目已配置log4j-to-slf4j桥接器，重新编译即可

3. **连接超时**
   - 检查网络连接
   - 增加连接超时时间
   - 验证服务器地址和端口

4. **内存不足**
   - 减少批处理大小
   - 增加JVM堆内存
   - 减少线程池大小

5. **索引创建失败**
   - 检查Elasticsearch集群状态
   - 验证索引名称是否合法
   - 确认有足够的磁盘空间

6. **数据格式错误**
   - 检查日期字段格式
   - 验证字符编码设置
   - 查看详细错误日志

### 日志级别调整

在 `logback.xml` 中调整日志级别：

```xml
<!-- 调试模式 -->
<logger name="com.example.migration" level="DEBUG"/>

<!-- 生产模式 -->
<logger name="com.example.migration" level="INFO"/>
```

## 开发指南

### 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/example/migration/
│   │       ├── config/          # 配置管理
│   │       ├── dao/             # 数据访问层
│   │       ├── database/        # 数据库管理
│   │       ├── elasticsearch/   # ES客户端管理
│   │       ├── model/           # 数据模型
│   │       ├── service/         # 业务服务层
│   │       └── MigrationApplication.java
│   └── resources/
│       ├── application.properties
│       └── logback.xml
└── test/
    └── java/
        └── com/example/migration/
            └── MigrationApplicationTest.java
```

### 扩展功能

1. **添加新的数据源**：实现新的DAO类
2. **自定义数据转换**：修改 `UserData` 模型类
3. **增加验证规则**：在服务层添加数据验证逻辑
4. **支持增量同步**：添加时间戳比较逻辑

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/mysql-es-migration
