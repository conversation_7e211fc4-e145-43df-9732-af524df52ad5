# MySQL到Elasticsearch数据迁移工具

这是一个专门用于将MySQL分库分表数据迁移到Elasticsearch的Java工具。支持200张分表的并发迁移，具有完善的错误处理和进度监控功能。

## 功能特性

### 数据迁移功能
- ✅ 支持MySQL分库分表数据迁移（200张表）
- ✅ 多线程并发处理，提高迁移效率
- ✅ 批量处理，避免内存溢出
- ✅ 完善的错误处理和重试机制
- ✅ 实时进度监控和统计
- ✅ 灵活的配置管理
- ✅ 详细的日志记录
- ✅ 自动创建Elasticsearch索引和映射
- ✅ 数据完整性验证

### 查询功能（新增）
- ✅ **交互式查询界面** - 友好的命令行交互体验
- ✅ **多种查询方式** - UID、手机号、姓名、工作地点等
- ✅ **复合条件查询** - 支持多字段组合查询
- ✅ **全文搜索** - 在所有字段中搜索关键字
- ✅ **分页浏览** - 支持大量结果的分页显示
- ✅ **高亮显示** - 匹配关键字高亮显示
- ✅ **查询统计** - 显示总数、耗时等统计信息
- ✅ **错误处理** - 完善的错误提示和处理机制

## 系统要求

- Java 8 或更高版本
- MySQL 5.7+ 或 8.0+
- Elasticsearch 7.x
- 至少 2GB 可用内存
- 足够的磁盘空间存储日志文件

## 快速开始

### 🚀 一键修复和运行（推荐）

如果遇到SSL连接或日期解析问题，直接运行一键修复脚本：

```bash
# Windows
fix-all-issues.bat

# Linux/Mac
chmod +x fix-all-issues.sh && ./fix-all-issues.sh
```

### 📋 手动配置步骤

#### 1. 配置数据库连接

编辑 `src/main/resources/application.properties` 文件：

```properties
# MySQL数据库配置
mysql.host=localhost
mysql.port=3306
mysql.database=your_database_name
mysql.username=your_username
mysql.password=your_password

# Elasticsearch配置
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=your_password
elasticsearch.index.name=user_data

# SSL配置（解决证书问题）
elasticsearch.ssl.skip-verification=true

# 迁移配置
migration.batch.size=1000
migration.thread.pool.size=10
migration.table.count=200
migration.table.prefix=data_
```

#### 2. 编译项目

```bash
mvn clean compile
```

#### 3. 运行测试工具

```bash
# 测试日期解析功能
java -cp "target/classes:target/dependency/*" com.example.migration.util.DateParsingTester

# 测试Elasticsearch连接
java -cp "target/classes:target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester

# 综合测试
java -cp "target/classes:target/dependency/*" com.example.migration.util.ComprehensiveTester
```

#### 4. 打包应用

```bash
mvn clean package
```

#### 5. 运行迁移

```bash
# 执行数据迁移
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar

# 或者明确指定迁移模式
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar --migration
```

### 🔍 使用查询功能

数据迁移完成后，可以使用内置的查询功能来搜索和浏览数据：

#### 启动查询模式

```bash
# 方法1：使用启动脚本（推荐）
# Windows
query.bat

# Linux/Mac
chmod +x query.sh && ./query.sh

# 方法2：直接运行JAR文件
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar --query
```

#### 查询功能特性

1. **UID精确查询** - 根据用户唯一标识查询
2. **手机号查询** - 根据手机号精确匹配
3. **姓名模糊查询** - 支持姓、名、全名的模糊搜索
4. **工作地点查询** - 根据工作城市或工作地点搜索
5. **复合条件查询** - 多个字段组合查询
6. **全文搜索** - 在所有字段中搜索关键字

#### 查询界面示例

```
========================================
Elasticsearch 交互式查询工具
========================================

请选择查询类型:
1. 根据UID查询
2. 根据手机号查询
3. 根据姓名模糊查询
4. 根据工作地点查询
5. 复合条件查询
6. 全文搜索
0. 退出
请输入选择 (0-6):
```

详细使用说明请参考：[查询功能使用指南](QUERY_GUIDE.md)

## 配置说明

### MySQL配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| mysql.host | MySQL服务器地址 | localhost | 是 |
| mysql.port | MySQL服务器端口 | 3306 | 否 |
| mysql.database | 数据库名称 | - | 是 |
| mysql.username | 用户名 | - | 是 |
| mysql.password | 密码 | - | 是 |

### Elasticsearch配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| elasticsearch.host | ES服务器地址 | localhost | 否 |
| elasticsearch.port | ES服务器端口 | 9200 | 否 |
| elasticsearch.scheme | 协议 | http | 否 |
| elasticsearch.username | 用户名（如需认证） | - | 否 |
| elasticsearch.password | 密码（如需认证） | - | 否 |
| elasticsearch.index.name | 目标索引名称 | user_data | 否 |

### 迁移配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| migration.batch.size | 批处理大小 | 1000 | 否 |
| migration.thread.pool.size | 线程池大小 | 10 | 否 |
| migration.table.count | 分表数量 | 200 | 否 |
| migration.table.prefix | 表名前缀 | data_ | 否 |

## 数据映射

### MySQL表结构
```sql
CREATE TABLE data_0 (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone_number VARCHAR(32),
    uid VARCHAR(64),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    gender VARCHAR(10),
    birth_place VARCHAR(100),
    workplace_city VARCHAR(50),
    marital_status VARCHAR(20),
    work_place TEXT,
    join_date VARCHAR(20),
    email VARCHAR(100),
    birth_date VARCHAR(20)
);
```

### Elasticsearch索引映射

工具会自动创建包含以下字段的索引：

- `mysql_id`: 原始MySQL记录ID
- `mysql_table`: 原始MySQL表名
- `uid`: 用户唯一标识
- `phone_number`: 手机号码
- `first_name`: 名字
- `last_name`: 姓氏
- `full_name`: 全名（自动生成）
- `name_initials`: 姓名首字母（自动生成）
- `gender`: 性别
- `birth_place`: 出生地
- `workplace_city`: 工作城市
- `work_place`: 工作地点
- `email`: 邮箱

**注意**: 为了简化数据模型和提高迁移效率，已移除以下字段：
- ~~`join_date`: 入职日期~~
- ~~`birth_date`: 出生日期~~
- ~~`marital_status`: 婚姻状况~~

## 文档ID规则

Elasticsearch文档ID格式：`{表名}_{MySQL记录ID}`

例如：
- 表 `data_0` 中ID为 `123456` 的记录 → ES文档ID为 `data_0_123456`
- 表 `data_99` 中ID为 `789012` 的记录 → ES文档ID为 `data_99_789012`

## 日志文件

程序运行时会在 `logs/` 目录下生成以下日志文件：

- `migration.log`: 完整的运行日志
- `migration-error.log`: 仅包含错误信息的日志

## 性能优化建议

1. **调整批处理大小**：根据服务器性能调整 `migration.batch.size`
2. **优化线程池大小**：根据CPU核心数调整 `migration.thread.pool.size`
3. **数据库连接池**：根据并发需求调整连接池配置
4. **JVM参数**：为大数据量迁移设置合适的堆内存

```bash
java -Xms2g -Xmx4g -jar migration-tool.jar
```

## 故障排除

### 常见问题

1. **MySQL连接失败（最常见）**
   ```
   Communications link failure
   Socket closed
   ```
   **快速解决**：
   ```bash
   # Windows
   fix-connection-issues.bat

   # Linux/Mac
   chmod +x fix-connection-issues.sh && ./fix-connection-issues.sh
   ```
   **详见**：[连接故障排除指南](CONNECTION_TROUBLESHOOTING_GUIDE.md)

2. **SSL证书验证失败**
   ```
   SSLHandshakeException: PKIX path building failed
   ```
   **解决方案**：
   - 开发环境：设置 `elasticsearch.ssl.skip-verification=true`
   - 生产环境：配置正确的SSL证书路径
   - 详见：[SSL配置指南](SSL_CONFIGURATION_GUIDE.md)

3. **大数据量处理超时**
   ```
   CompletableFuture.allOf() NullPointerException
   ```
   **解决方案**：
   - 减少批处理大小：`migration.batch.size=500`
   - 减少线程数：`migration.thread.pool.size=8`
   - 执行MySQL优化脚本：`mysql-optimization.sql`

4. **连接池耗尽**
   ```
   HikariPool - Connection is not available
   ```
   **解决方案**：
   - 增加连接池大小：`mysql.pool.maximum-pool-size=30`
   - 启用连接验证：`migration.connection.test-on-borrow=true`

5. **内存不足**
   - 增加JVM堆内存：`-Xms4g -Xmx8g`
   - 减少批处理大小
   - 减少线程池大小

6. **网络超时**
   - 检查网络连接稳定性
   - 增加超时时间配置
   - 使用有线网络连接

### 日志级别调整

在 `logback.xml` 中调整日志级别：

```xml
<!-- 调试模式 -->
<logger name="com.example.migration" level="DEBUG"/>

<!-- 生产模式 -->
<logger name="com.example.migration" level="INFO"/>
```

## 开发指南

### 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/example/migration/
│   │       ├── config/          # 配置管理
│   │       ├── dao/             # 数据访问层
│   │       ├── database/        # 数据库管理
│   │       ├── elasticsearch/   # ES客户端管理
│   │       ├── model/           # 数据模型
│   │       ├── service/         # 业务服务层
│   │       └── MigrationApplication.java
│   └── resources/
│       ├── application.properties
│       └── logback.xml
└── test/
    └── java/
        └── com/example/migration/
            └── MigrationApplicationTest.java
```

### 扩展功能

1. **添加新的数据源**：实现新的DAO类
2. **自定义数据转换**：修改 `UserData` 模型类
3. **增加验证规则**：在服务层添加数据验证逻辑
4. **支持增量同步**：添加时间戳比较逻辑

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/mysql-es-migration
