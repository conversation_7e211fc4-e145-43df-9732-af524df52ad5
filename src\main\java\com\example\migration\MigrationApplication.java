package com.example.migration;

import com.example.migration.config.MigrationConfig;
import com.example.migration.service.MigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据迁移应用程序主类
 * MySQL分库分表数据迁移到Elasticsearch
 */
public class MigrationApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationApplication.class);
    
    public static void main(String[] args) {
        logger.info("=== MySQL到Elasticsearch数据迁移工具 ===");
        logger.info("版本: 1.0.0");
        logger.info("作者: 数据迁移团队");
        logger.info("========================================");
        
        try {
            // 处理命令行参数
            processCommandLineArgs(args);
            
            // 加载配置
            MigrationConfig config = new MigrationConfig();
            
            // 创建迁移服务
            MigrationService migrationService = new MigrationService(config);
            
            // 执行迁移
            migrationService.executeMigration();
            
            logger.info("数据迁移成功完成！");
            System.exit(0);
            
        } catch (Exception e) {
            logger.error("数据迁移失败", e);
            System.err.println("错误: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * 处理命令行参数
     */
    private static void processCommandLineArgs(String[] args) {
        if (args.length > 0) {
            for (String arg : args) {
                switch (arg.toLowerCase()) {
                    case "--help":
                    case "-h":
                        printHelp();
                        System.exit(0);
                        break;
                    case "--version":
                    case "-v":
                        printVersion();
                        System.exit(0);
                        break;
                    default:
                        if (arg.startsWith("-")) {
                            logger.warn("未知参数: {}", arg);
                        }
                        break;
                }
            }
        }
    }
    
    /**
     * 打印帮助信息
     */
    private static void printHelp() {
        System.out.println("MySQL到Elasticsearch数据迁移工具");
        System.out.println();
        System.out.println("用法: java -jar migration-tool.jar [选项]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  -h, --help     显示此帮助信息");
        System.out.println("  -v, --version  显示版本信息");
        System.out.println();
        System.out.println("配置文件:");
        System.out.println("  程序会自动加载 application.properties 配置文件");
        System.out.println("  请确保配置文件在类路径中或与JAR文件同目录");
        System.out.println();
        System.out.println("必要配置项:");
        System.out.println("  mysql.host              - MySQL服务器地址");
        System.out.println("  mysql.port              - MySQL服务器端口");
        System.out.println("  mysql.database          - MySQL数据库名");
        System.out.println("  mysql.username          - MySQL用户名");
        System.out.println("  mysql.password          - MySQL密码");
        System.out.println("  elasticsearch.host      - Elasticsearch服务器地址");
        System.out.println("  elasticsearch.port      - Elasticsearch服务器端口");
        System.out.println("  elasticsearch.index.name - 目标索引名称");
        System.out.println();
        System.out.println("可选配置项:");
        System.out.println("  migration.batch.size    - 批处理大小 (默认: 1000)");
        System.out.println("  migration.thread.pool.size - 线程池大小 (默认: 10)");
        System.out.println("  migration.table.count   - 分表数量 (默认: 200)");
        System.out.println("  migration.table.prefix  - 表名前缀 (默认: data_)");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -jar migration-tool.jar");
        System.out.println();
        System.out.println("日志文件:");
        System.out.println("  程序运行日志会保存到 logs/ 目录下");
        System.out.println("  - migration.log: 完整日志");
        System.out.println("  - migration-error.log: 错误日志");
        System.out.println();
        System.out.println("注意事项:");
        System.out.println("  1. 确保MySQL和Elasticsearch服务正常运行");
        System.out.println("  2. 确保有足够的磁盘空间存储日志文件");
        System.out.println("  3. 建议在低峰期执行大量数据迁移");
        System.out.println("  4. 迁移过程中请勿中断程序运行");
    }
    
    /**
     * 打印版本信息
     */
    private static void printVersion() {
        System.out.println("MySQL到Elasticsearch数据迁移工具");
        System.out.println("版本: 1.0.0");
        System.out.println("构建日期: 2024-01-01");
        System.out.println("Java版本要求: 8+");
        System.out.println("支持的Elasticsearch版本: 7.x");
        System.out.println("支持的MySQL版本: 5.7+, 8.0+");
    }
}
