# MySQL连接问题故障排除指南

本文档详细说明如何解决MySQL到Elasticsearch数据迁移过程中的连接失败问题。

## 🔍 问题症状分析

### 常见错误信息
```
java.lang.RuntimeException: 数据迁移任务执行失败
Caused by: java.lang.NullPointerException
	at java.util.concurrent.CompletableFuture.andTree(CompletableFuture.java:1520)
	at java.util.concurrent.CompletableFuture.allOf(CompletableFuture.java:2419)

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure
Caused by: java.net.SocketException: Socket closed
```

### 问题根本原因
1. **MySQL服务器超时**: 默认8小时后关闭空闲连接
2. **连接池配置不当**: 连接验证和回收机制不完善
3. **网络超时**: 大数据量传输时网络连接中断
4. **并发冲突**: 多线程同时访问导致连接竞争
5. **内存压力**: 大量数据处理导致系统资源不足

## 🛠️ 解决方案实施

### 第一步：应用配置优化

#### 1.1 更新应用配置文件
```properties
# 优化后的连接池配置
mysql.pool.maximum-pool-size=30
mysql.pool.minimum-idle=10
mysql.pool.connection-timeout=60000
mysql.pool.idle-timeout=300000
mysql.pool.max-lifetime=900000
mysql.pool.keepalive-time=30000
mysql.pool.validation-timeout=5000
mysql.pool.leak-detection-threshold=60000

# 重试和恢复配置
migration.retry.max-attempts=3
migration.retry.delay-seconds=5
migration.connection.test-on-borrow=true
migration.connection.test-while-idle=true

# 优化迁移参数
migration.batch.size=500
migration.thread.pool.size=8
```

#### 1.2 重新编译应用
```bash
mvn clean package -DskipTests
```

### 第二步：MySQL服务器优化

#### 2.1 执行MySQL优化脚本
```bash
# 连接到MySQL
mysql -u root -p

# 执行优化脚本
source mysql-optimization.sql;
```

#### 2.2 修改MySQL配置文件
在 `my.cnf` (Linux) 或 `my.ini` (Windows) 中添加：
```ini
[mysqld]
# 连接和超时配置
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 600
net_write_timeout = 600
max_connections = 500
max_user_connections = 450

# 数据包配置
max_allowed_packet = 1G

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 120
```

#### 2.3 重启MySQL服务
```bash
# Linux
sudo systemctl restart mysql

# Windows
net stop mysql
net start mysql
```

### 第三步：运行诊断工具

#### 3.1 连接诊断
```bash
java -cp "target/classes:target/dependency/*" com.example.migration.util.ConnectionDiagnosticTool
```

#### 3.2 综合测试
```bash
java -cp "target/classes:target/dependency/*" com.example.migration.util.ComprehensiveTester
```

### 第四步：执行数据迁移

#### 4.1 使用优化后的启动脚本
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

#### 4.2 监控迁移过程
- 观察日志输出中的连接池状态
- 注意重试机制的执行情况
- 监控系统资源使用情况

## 📊 性能优化建议

### 硬件资源配置
- **内存**: 建议至少8GB，推荐16GB+
- **CPU**: 多核处理器，建议8核+
- **网络**: 稳定的网络连接，避免无线网络
- **磁盘**: SSD硬盘提供更好的I/O性能

### JVM参数优化
```bash
java -Xms4g -Xmx8g -XX:+UseG1GC -XX:+UseStringDeduplication \
     -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m \
     -jar target/mysql-to-elasticsearch-migration-1.0.0.jar
```

### 分批处理策略
1. **小批量**: 减少批处理大小到500条
2. **少线程**: 减少并发线程数到8个
3. **分时段**: 在系统负载较低时执行迁移
4. **分表处理**: 可以分批处理表，而不是一次性处理所有表

## 🔧 故障排除步骤

### 步骤1: 验证基础环境
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查网络连接
telnet mysql_host 3306

# 检查Java版本
java -version
```

### 步骤2: 检查配置
```bash
# 验证MySQL配置
mysql -u root -p -e "SHOW VARIABLES LIKE '%timeout%';"
mysql -u root -p -e "SHOW VARIABLES LIKE 'max_connections';"
mysql -u root -p -e "SHOW VARIABLES LIKE 'max_allowed_packet';"

# 检查当前连接状态
mysql -u root -p -e "SHOW STATUS LIKE 'Connections';"
mysql -u root -p -e "SHOW PROCESSLIST;"
```

### 步骤3: 运行诊断工具
```bash
# 连接诊断
java -cp "target/classes:target/dependency/*" com.example.migration.util.ConnectionDiagnosticTool

# 如果诊断失败，检查错误信息并相应调整配置
```

### 步骤4: 逐步测试
```bash
# 1. 先测试单表迁移
# 修改配置文件中的 migration.table.count=1

# 2. 测试小批量数据
# 修改配置文件中的 migration.batch.size=100

# 3. 逐步增加并发度和批量大小
```

## ⚠️ 常见问题和解决方案

### 问题1: 连接池耗尽
**症状**: `HikariPool - Connection is not available`
**解决**: 增加连接池大小，检查连接泄漏

### 问题2: 内存不足
**症状**: `OutOfMemoryError`
**解决**: 增加JVM堆内存，减少批处理大小

### 问题3: 网络超时
**症状**: `SocketTimeoutException`
**解决**: 增加网络超时时间，检查网络稳定性

### 问题4: MySQL锁等待
**症状**: `Lock wait timeout exceeded`
**解决**: 增加锁等待超时时间，优化查询

### 问题5: 磁盘空间不足
**症状**: `No space left on device`
**解决**: 清理磁盘空间，优化日志配置

## 📈 监控和维护

### 实时监控指标
1. **连接池状态**: 活跃连接数、空闲连接数
2. **系统资源**: CPU使用率、内存使用率
3. **网络状态**: 网络延迟、丢包率
4. **数据库状态**: 连接数、锁等待、慢查询

### 日志分析
- 查看 `logs/migration.log` 了解详细执行情况
- 查看 `logs/migration-error.log` 分析错误原因
- 监控MySQL错误日志

### 定期维护
1. **清理日志**: 定期清理过期的日志文件
2. **更新统计**: 更新MySQL表统计信息
3. **优化索引**: 检查和优化数据库索引
4. **监控性能**: 定期运行性能测试

## 🎯 最佳实践总结

1. **预先测试**: 在生产环境前充分测试
2. **分批执行**: 大数据量分批处理
3. **监控资源**: 实时监控系统资源
4. **备份数据**: 迁移前备份重要数据
5. **错误处理**: 完善的错误处理和重试机制
6. **文档记录**: 记录配置和问题解决过程

通过以上优化措施，可以有效解决MySQL连接失败问题，确保大数据量迁移的稳定性和可靠性。
