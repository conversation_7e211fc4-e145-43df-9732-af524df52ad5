package com.example.migration.service;

import com.example.migration.config.MigrationConfig;
import com.example.migration.dao.UserDataDao;
import com.example.migration.database.DatabaseManager;
import com.example.migration.elasticsearch.ElasticsearchManager;
import com.example.migration.model.UserData;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 数据迁移服务类
 * 负责协调整个数据迁移过程
 */
public class MigrationService {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationService.class);
    
    private MigrationConfig config;
    private DatabaseManager databaseManager;
    private ElasticsearchManager elasticsearchManager;
    private UserDataDao userDataDao;
    private ObjectMapper objectMapper;
    private ExecutorService executorService;
    
    // 统计信息
    private AtomicLong totalProcessed = new AtomicLong(0);
    private AtomicLong totalSuccess = new AtomicLong(0);
    private AtomicLong totalFailed = new AtomicLong(0);
    private long startTime;

    // 失败表跟踪和重试机制
    private Map<String, Integer> failedTables = new ConcurrentHashMap<>();
    private Map<String, String> tableFailureReasons = new ConcurrentHashMap<>();
    
    public MigrationService(MigrationConfig config) {
        this.config = config;
        this.databaseManager = new DatabaseManager(config);
        this.elasticsearchManager = new ElasticsearchManager(config);
        this.userDataDao = new UserDataDao(databaseManager.getDataSource());
        
        // 配置JSON序列化器
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        
        // 创建线程池
        this.executorService = Executors.newFixedThreadPool(config.getThreadPoolSize());
    }
    
    /**
     * 执行完整的数据迁移
     */
    public void executeMigration() {
        logger.info("=== 开始数据迁移 ===");
        startTime = System.currentTimeMillis();
        
        try {
            // 1. 验证配置
            validateConfiguration();
            
            // 2. 准备Elasticsearch索引
            prepareElasticsearchIndex();
            
            // 3. 验证MySQL分表
            validateMysqlTables();
            
            // 4. 执行数据迁移
            performDataMigration();
            
            // 5. 输出迁移结果
            printMigrationSummary();
            
        } catch (Exception e) {
            logger.error("数据迁移过程中发生错误", e);
            throw new RuntimeException("数据迁移失败", e);
        } finally {
            cleanup();
        }
        
        logger.info("=== 数据迁移完成 ===");
    }
    
    /**
     * 验证配置
     */
    private void validateConfiguration() {
        logger.info("正在验证配置...");
        config.validateConfig();
        config.printConfig();
    }
    
    /**
     * 准备Elasticsearch索引
     */
    private void prepareElasticsearchIndex() {
        logger.info("正在准备Elasticsearch索引...");
        
        String indexName = config.getElasticsearchIndexName();
        
        // 检查索引是否存在
        if (elasticsearchManager.indexExists(indexName)) {
            logger.warn("索引 {} 已存在", indexName);
            
            // 可以选择删除重建或者继续使用
            // 这里选择继续使用现有索引
            logger.info("将使用现有索引进行迁移");
        } else {
            // 创建新索引
            boolean created = elasticsearchManager.createIndex(indexName);
            if (!created) {
                throw new RuntimeException("创建Elasticsearch索引失败");
            }
        }
    }
    
    /**
     * 验证MySQL分表
     */
    private void validateMysqlTables() {
        logger.info("正在验证MySQL分表...");
        databaseManager.validateTables();
        
        long totalRows = databaseManager.getTotalRowCount();
        logger.info("预计需要迁移 {} 条记录", totalRows);
    }
    
    /**
     * 执行数据迁移
     */
    private void performDataMigration() {
        logger.info("开始执行数据迁移...");

        int tableCount = config.getTableCount();
        String tablePrefix = config.getTablePrefix();

        // 为每个表创建迁移任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < tableCount; i++) {
            String tableName = tablePrefix + i;
            final int tableIndex = i;

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    migrateTable(tableName, tableIndex);
                } catch (Exception e) {
                    logger.error("表 {} 迁移失败", tableName, e);
                    failedTables.put(tableName, tableIndex);
                    tableFailureReasons.put(tableName, e.getMessage());

                    // 检查是否是连接问题
                    if (isConnectionError(e)) {
                        logger.warn("检测到连接问题，尝试刷新连接池");
                        databaseManager.refreshPool();
                    }
                }
            }, executorService);

            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            logger.error("等待迁移任务完成时发生错误", e);
            // 不立即抛出异常，而是尝试重试失败的表
        }

        // 重试失败的表
        retryFailedTables();
    }

    /**
     * 检查是否是连接错误
     */
    private boolean isConnectionError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        return message.contains("Communications link failure") ||
               message.contains("Socket closed") ||
               message.contains("Connection timed out") ||
               message.contains("Connection reset") ||
               message.contains("No operations allowed after connection closed");
    }

    /**
     * 重试失败的表
     */
    private void retryFailedTables() {
        if (failedTables.isEmpty()) {
            logger.info("所有表迁移成功，无需重试");
            return;
        }

        logger.info("开始重试 {} 张失败的表", failedTables.size());
        int maxRetries = config.getIntProperty("migration.retry.max-attempts", 3);

        for (int retry = 1; retry <= maxRetries; retry++) {
            if (failedTables.isEmpty()) {
                break;
            }

            logger.info("第 {} 次重试，剩余失败表数: {}", retry, failedTables.size());

            Map<String, Integer> currentFailedTables = new ConcurrentHashMap<>(failedTables);
            failedTables.clear();
            tableFailureReasons.clear();

            List<CompletableFuture<Void>> retryFutures = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : currentFailedTables.entrySet()) {
                String tableName = entry.getKey();
                int tableIndex = entry.getValue();

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        logger.info("重试迁移表: {}", tableName);
                        migrateTable(tableName, tableIndex);
                        logger.info("表 {} 重试成功", tableName);
                    } catch (Exception e) {
                        logger.error("表 {} 重试失败", tableName, e);
                        failedTables.put(tableName, tableIndex);
                        tableFailureReasons.put(tableName, e.getMessage());
                    }
                }, executorService);

                retryFutures.add(future);
            }

            try {
                CompletableFuture.allOf(retryFutures.toArray(new CompletableFuture[0])).get();
            } catch (Exception e) {
                logger.warn("重试过程中发生错误", e);
            }

            // 等待一段时间再进行下次重试
            if (retry < maxRetries && !failedTables.isEmpty()) {
                try {
                    int delaySeconds = config.getIntProperty("migration.retry.delay-seconds", 5);
                    Thread.sleep(delaySeconds * 1000L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (!failedTables.isEmpty()) {
            logger.error("经过 {} 次重试后，仍有 {} 张表迁移失败", maxRetries, failedTables.size());
            for (Map.Entry<String, String> entry : tableFailureReasons.entrySet()) {
                logger.error("失败表: {}, 原因: {}", entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 迁移单个表的数据
     */
    private void migrateTable(String tableName, int tableIndex) {
        logger.info("开始迁移表: {} (索引: {})", tableName, tableIndex);
        
        try {
            // 检查表是否存在
            if (!databaseManager.tableExists(tableName)) {
                logger.warn("表 {} 不存在，跳过迁移", tableName);
                return;
            }
            
            // 获取表的记录数
            long tableRowCount = databaseManager.getTableRowCount(tableName);
            if (tableRowCount == 0) {
                logger.info("表 {} 没有数据，跳过迁移", tableName);
                return;
            }
            
            logger.info("表 {} 共有 {} 条记录", tableName, tableRowCount);
            
            // 分批处理数据
            UserDataDao.UserDataBatchIterator iterator = userDataDao.getBatchIterator(tableName, config.getBatchSize());
            
            long tableProcessed = 0;
            long tableSuccess = 0;
            long tableFailed = 0;
            int batchIndex = 0;
            
            while (iterator.hasNext()) {
                batchIndex++;
                logger.info("正在处理批次 {} , 已处理 ({}/{})", batchIndex, tableProcessed, tableRowCount);
                List<UserData> batch = iterator.next();
                if (batch.isEmpty()) {
                    break;
                }
                
                // 处理批次数据
                BatchResult result = processBatch(batch, tableName);
                
                tableProcessed += result.processed;
                tableSuccess += result.success;
                tableFailed += result.failed;
                
                // 更新全局统计
                totalProcessed.addAndGet(result.processed);
                totalSuccess.addAndGet(result.success);
                totalFailed.addAndGet(result.failed);
                
                // 输出进度和健康检查
                if (tableProcessed % (config.getBatchSize() * 10) == 0 || !iterator.hasNext()) {
                    double progress = iterator.getProgress();
                    logger.info("表 {} 迁移进度: {}",
                            tableName,
                            String.format("%.2f%% (%d/%d)",
                                    (double)tableProcessed/tableRowCount*100,
                                    tableProcessed,
                                    tableRowCount));

                    // 定期检查连接池健康状态
                    if (tableProcessed % (config.getBatchSize() * 20) == 0) {
                        if (!databaseManager.isHealthy()) {
                            logger.warn("检测到连接池不健康，尝试刷新");
                            databaseManager.refreshPool();
                        }
                        databaseManager.printPoolStatus();
                    }
                }
            }
            
            logger.info("表 {} 迁移完成: 处理 {}, 成功 {}, 失败 {}", 
                    tableName, tableProcessed, tableSuccess, tableFailed);
            
        } catch (Exception e) {
            logger.error("迁移表 {} 时发生错误", tableName, e);
        }
    }
    
    /**
     * 处理数据批次
     */
    private BatchResult processBatch(List<UserData> batch, String tableName) {
        BatchResult result = new BatchResult();
        
        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            for (UserData userData : batch) {
                try {
                    // 转换为JSON
                    String jsonSource = objectMapper.writeValueAsString(userData);
                    
                    // 创建索引请求
                    IndexRequest indexRequest = elasticsearchManager.createIndexRequest(
                            config.getElasticsearchIndexName(),
                            userData.generateDocumentId(),
                            jsonSource
                    );
                    
                    bulkRequest.add(indexRequest);
                    result.processed++;
                    
                } catch (Exception e) {
                    logger.error("处理用户数据时发生错误: {}", userData, e);
                    result.failed++;
                }
            }
            
            // 执行批量索引
            if (bulkRequest.numberOfActions() > 0) {
                boolean success = elasticsearchManager.bulkIndex(bulkRequest);
                if (success) {
                    result.success += bulkRequest.numberOfActions();
                } else {
                    result.failed += bulkRequest.numberOfActions();
                    result.success = 0; // 整个批次失败
                }
            }
            
        } catch (Exception e) {
            logger.error("处理批次数据时发生错误", e);
            result.failed += batch.size();
            result.success = 0;
        }
        
        return result;
    }
    
    /**
     * 输出迁移摘要
     */
    private void printMigrationSummary() {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        logger.info("=== 迁移摘要 ===");
        logger.info("总处理记录数: {}", totalProcessed.get());
        logger.info("成功记录数: {}", totalSuccess.get());
        logger.info("失败记录数: {}", totalFailed.get());
        logger.info("成功率: {:.2f}%", 
                totalProcessed.get() > 0 ? (double) totalSuccess.get() / totalProcessed.get() * 100 : 0);
        logger.info("总耗时: {} 秒", duration / 1000);
        logger.info("平均速度: {:.2f} 记录/秒", 
                duration > 0 ? (double) totalProcessed.get() / (duration / 1000.0) : 0);
        logger.info("===============");
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        logger.info("正在清理资源...");
        
        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 关闭数据库连接
        if (databaseManager != null) {
            databaseManager.close();
        }
        
        // 关闭Elasticsearch客户端
        if (elasticsearchManager != null) {
            elasticsearchManager.close();
        }
        
        logger.info("资源清理完成");
    }
    
    /**
     * 批次处理结果
     */
    private static class BatchResult {
        long processed = 0;
        long success = 0;
        long failed = 0;
    }
}
