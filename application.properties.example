# MySQL到Elasticsearch数据迁移工具配置文件示例
# 请复制此文件为 application.properties 并修改相应配置

# ================================
# MySQL数据库配置
# ================================
# MySQL服务器地址
mysql.host=localhost
# MySQL服务器端口
mysql.port=3306
# 数据库名称（必填）
mysql.database=your_database_name
# 数据库用户名（必填）
mysql.username=your_username
# 数据库密码（必填）
mysql.password=your_password
# JDBC驱动类名
mysql.driver=com.mysql.cj.jdbc.Driver

# ================================
# 数据库连接池配置
# ================================
# 连接池最大连接数
mysql.pool.maximum-pool-size=20
# 连接池最小空闲连接数
mysql.pool.minimum-idle=5
# 连接超时时间（毫秒）
mysql.pool.connection-timeout=30000
# 空闲连接超时时间（毫秒）
mysql.pool.idle-timeout=600000
# 连接最大生存时间（毫秒）
mysql.pool.max-lifetime=1800000

# ================================
# Elasticsearch配置
# ================================
# Elasticsearch服务器地址
elasticsearch.host=localhost
# Elasticsearch服务器端口
elasticsearch.port=9200
# 连接协议（http或https）
elasticsearch.scheme=http
# Elasticsearch用户名（如果启用了安全认证）
elasticsearch.username=
# Elasticsearch密码（如果启用了安全认证）
elasticsearch.password=
# 目标索引名称
elasticsearch.index.name=user_data

# ================================
# SSL/TLS安全配置
# ================================
# 是否跳过SSL证书验证（仅开发环境使用！）
# 生产环境必须设为false并配置正确的证书
elasticsearch.ssl.skip-verification=false

# SSL证书配置（可选）
# 自定义CA证书路径（PEM格式）
elasticsearch.ssl.certificate.path=
# 客户端证书路径（用于双向认证）
elasticsearch.ssl.client.certificate.path=
# 客户端私钥路径（用于双向认证）
elasticsearch.ssl.client.key.path=

# ================================
# 数据迁移配置
# ================================
# 批处理大小（每次处理的记录数）
# 建议值：500-2000，根据服务器性能调整
migration.batch.size=1000

# 线程池大小（并发处理的线程数）
# 建议值：CPU核心数的1-2倍
migration.thread.pool.size=10

# 分表数量（总共有多少张分表）
migration.table.count=200

# 表名前缀（分表的命名前缀）
migration.table.prefix=data_

# ================================
# 日志配置
# ================================
# 根日志级别
logging.level.root=INFO
# 应用程序日志级别（DEBUG, INFO, WARN, ERROR）
logging.level.com.example.migration=INFO

# ================================
# 高级配置（一般不需要修改）
# ================================
# Elasticsearch连接超时时间（毫秒）
elasticsearch.connection.timeout=5000
# Elasticsearch请求超时时间（毫秒）
elasticsearch.socket.timeout=60000

# MySQL查询超时时间（秒）
mysql.query.timeout=300

# 批量索引超时时间（毫秒）
elasticsearch.bulk.timeout=60000

# ================================
# 性能调优建议
# ================================
# 对于大数据量迁移（百万级以上记录）：
# - 增加批处理大小：migration.batch.size=2000
# - 适当增加线程数：migration.thread.pool.size=15
# - 增加连接池大小：mysql.pool.maximum-pool-size=30
# - 设置更大的JVM堆内存：java -Xms2g -Xmx4g

# 对于小数据量迁移（十万级以下记录）：
# - 减少批处理大小：migration.batch.size=500
# - 减少线程数：migration.thread.pool.size=5
# - 减少连接池大小：mysql.pool.maximum-pool-size=10

# 对于网络环境较差的情况：
# - 增加连接超时时间：mysql.pool.connection-timeout=60000
# - 增加ES超时时间：elasticsearch.socket.timeout=120000
# - 减少并发线程数：migration.thread.pool.size=5

# ================================
# 示例配置（请根据实际环境修改）
# ================================
# 开发环境示例：
# mysql.host=dev-mysql.example.com
# mysql.database=dev_user_db
# mysql.username=dev_user
# mysql.password=dev_password123
# elasticsearch.host=dev-es.example.com
# migration.batch.size=500
# migration.thread.pool.size=5

# 生产环境示例：
# mysql.host=prod-mysql.example.com
# mysql.database=prod_user_db
# mysql.username=migration_user
# mysql.password=secure_password_here
# elasticsearch.host=prod-es.example.com
# elasticsearch.username=elastic
# elasticsearch.password=elastic_password
# migration.batch.size=2000
# migration.thread.pool.size=15
