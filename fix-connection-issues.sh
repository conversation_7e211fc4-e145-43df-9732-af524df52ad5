#!/bin/bash

# MySQL连接问题一键修复脚本

echo "========================================"
echo "MySQL连接问题一键修复工具"
echo "========================================"
echo

echo "正在诊断和修复MySQL连接问题..."
echo

echo "步骤1: 检查项目结构..."
if [ ! -f "pom.xml" ]; then
    echo "❌ 未找到Maven项目文件"
    exit 1
fi

if [ ! -f "src/main/resources/application.properties" ]; then
    echo "❌ 未找到配置文件"
    exit 1
fi
echo "✅ 项目结构检查通过"

echo
echo "步骤2: 备份原始配置..."
if [ -f "src/main/resources/application.properties.backup" ]; then
    echo "发现已有备份文件"
else
    cp "src/main/resources/application.properties" "src/main/resources/application.properties.backup"
    echo "✅ 配置文件已备份"
fi

echo
echo "步骤3: 应用连接优化配置..."

# 更新连接池配置
sed -i 's/mysql.pool.maximum-pool-size=.*/mysql.pool.maximum-pool-size=30/g' src/main/resources/application.properties
sed -i 's/mysql.pool.minimum-idle=.*/mysql.pool.minimum-idle=10/g' src/main/resources/application.properties
sed -i 's/mysql.pool.connection-timeout=.*/mysql.pool.connection-timeout=60000/g' src/main/resources/application.properties
sed -i 's/mysql.pool.idle-timeout=.*/mysql.pool.idle-timeout=300000/g' src/main/resources/application.properties
sed -i 's/mysql.pool.max-lifetime=.*/mysql.pool.max-lifetime=900000/g' src/main/resources/application.properties

# 更新迁移配置
sed -i 's/migration.batch.size=.*/migration.batch.size=500/g' src/main/resources/application.properties
sed -i 's/migration.thread.pool.size=.*/migration.thread.pool.size=8/g' src/main/resources/application.properties

# 添加新的配置项（如果不存在）
if ! grep -q "mysql.pool.keepalive-time" src/main/resources/application.properties; then
    echo "mysql.pool.keepalive-time=30000" >> src/main/resources/application.properties
fi

if ! grep -q "migration.retry.max-attempts" src/main/resources/application.properties; then
    echo "migration.retry.max-attempts=3" >> src/main/resources/application.properties
    echo "migration.retry.delay-seconds=5" >> src/main/resources/application.properties
    echo "migration.connection.test-on-borrow=true" >> src/main/resources/application.properties
    echo "migration.connection.test-while-idle=true" >> src/main/resources/application.properties
fi

echo "✅ 连接优化配置已应用"

echo
echo "步骤4: 编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "❌ 编译失败，请检查代码错误"
    exit 1
fi
echo "✅ 项目编译成功"

echo
echo "步骤5: 运行连接诊断..."
echo "正在执行连接诊断测试..."
java -cp "target/classes:target/dependency/*" com.example.migration.util.ConnectionDiagnosticTool > connection_diagnostic.log 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 连接诊断测试通过"
else
    echo "⚠️ 连接诊断发现问题，请查看 connection_diagnostic.log"
    echo
    echo "常见问题解决方案:"
    echo "1. 检查MySQL服务是否运行"
    echo "2. 验证数据库连接信息"
    echo "3. 执行MySQL优化脚本: mysql -u root -p < mysql-optimization.sql"
    echo "4. 检查网络连接"
fi

echo
echo "步骤6: 打包应用程序..."
mvn package -DskipTests -q
if [ $? -ne 0 ]; then
    echo "❌ 打包失败"
    exit 1
fi
echo "✅ 应用程序打包成功"

echo
echo "步骤7: 最终验证..."
echo "正在进行最终连接验证..."
java -cp "target/classes:target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester > es_connection_test.log 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Elasticsearch连接验证通过"
else
    echo "⚠️ Elasticsearch连接验证失败，请查看 es_connection_test.log"
fi

echo
echo "========================================"
echo "🔧 修复完成报告"
echo "========================================"
echo

echo "已应用的优化措施:"
echo "✅ 1. 优化了数据库连接池配置"
echo "✅ 2. 增加了连接重试机制"
echo "✅ 3. 减少了批处理大小和线程数"
echo "✅ 4. 添加了连接健康检查"
echo "✅ 5. 增强了错误处理和恢复机制"

echo
echo "下一步建议:"
echo "1. 执行MySQL服务器优化:"
echo "   mysql -u root -p < mysql-optimization.sql"
echo
echo "2. 如果仍有连接问题，请:"
echo "   - 检查MySQL服务器配置"
echo "   - 验证网络连接稳定性"
echo "   - 考虑分批处理数据"
echo
echo "3. 运行数据迁移:"
echo "   java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar"
echo

echo "相关文档:"
echo "- CONNECTION_TROUBLESHOOTING_GUIDE.md: 详细故障排除指南"
echo "- mysql-optimization.sql: MySQL服务器优化脚本"
echo "- connection_diagnostic.log: 连接诊断日志"
echo

read -p "是否立即运行数据迁移? (y/n): " choice
if [[ $choice =~ ^[Yy]$ ]]; then
    echo
    echo "正在启动数据迁移..."
    java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar
else
    echo
    echo "修复完成，您可以稍后手动运行迁移程序"
    echo "建议先执行MySQL优化脚本，然后再运行迁移"
fi

echo
echo "如果遇到问题，请查看详细文档: CONNECTION_TROUBLESHOOTING_GUIDE.md"
