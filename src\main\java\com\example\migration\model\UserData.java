package com.example.migration.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户数据模型类
 * 对应Elasticsearch索引结构和MySQL表结构
 */
public class UserData {
    
    /**
     * MySQL原始ID
     */
    @JsonProperty("mysql_id")
    private Integer mysqlId;
    
    /**
     * MySQL表名
     */
    @JsonProperty("mysql_table")
    private String mysqlTable;
    
    /**
     * 用户唯一标识
     */
    @JsonProperty("uid")
    private String uid;
    
    /**
     * 手机号码
     */
    @JsonProperty("phone_number")
    private String phoneNumber;
    
    /**
     * 名字
     */
    @JsonProperty("first_name")
    private String firstName;
    
    /**
     * 姓氏
     */
    @JsonProperty("last_name")
    private String lastName;
    
    /**
     * 全名（由first_name和last_name组合而成，在ES中通过copy_to实现）
     */
    @JsonProperty("full_name")
    private String fullName;
    
    /**
     * 姓名首字母
     */
    @JsonProperty("name_initials")
    private String nameInitials;
    
    /**
     * 性别
     */
    @JsonProperty("gender")
    private String gender;

    /**
     * 出生地
     */
    @JsonProperty("birth_place")
    private String birthPlace;

    /**
     * 工作地城市
     */
    @JsonProperty("workplace_city")
    private String workplaceCity;

    /**
     * 工作地点
     */
    @JsonProperty("work_place")
    private String workPlace;

    /**
     * 邮箱
     */
    @JsonProperty("email")
    private String email;
    
    // 构造函数
    public UserData() {}
    
    // Getter和Setter方法
    public Integer getMysqlId() {
        return mysqlId;
    }
    
    public void setMysqlId(Integer mysqlId) {
        this.mysqlId = mysqlId;
    }
    
    public String getMysqlTable() {
        return mysqlTable;
    }
    
    public void setMysqlTable(String mysqlTable) {
        this.mysqlTable = mysqlTable;
    }
    
    public String getUid() {
        return uid;
    }
    
    public void setUid(String uid) {
        this.uid = uid;
    }
    
    public String getPhoneNumber() {
        return phoneNumber;
    }
    
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getNameInitials() {
        return nameInitials;
    }
    
    public void setNameInitials(String nameInitials) {
        this.nameInitials = nameInitials;
    }
    
    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getWorkplaceCity() {
        return workplaceCity;
    }

    public void setWorkplaceCity(String workplaceCity) {
        this.workplaceCity = workplaceCity;
    }

    public String getWorkPlace() {
        return workPlace;
    }

    public void setWorkPlace(String workPlace) {
        this.workPlace = workPlace;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    
    /**
     * 生成Elasticsearch文档ID
     * 格式：表名_MySQL记录ID
     */
    public String generateDocumentId() {
        return mysqlTable + "_" + mysqlId;
    }
    
    /**
     * 生成姓名首字母
     */
    public void generateNameInitials() {
        StringBuilder initials = new StringBuilder();
        if (firstName != null && !firstName.trim().isEmpty()) {
            initials.append(firstName.trim().charAt(0));
        }
        if (lastName != null && !lastName.trim().isEmpty()) {
            initials.append(lastName.trim().charAt(0));
        }
        this.nameInitials = initials.toString().toUpperCase();
    }
    
    /**
     * 生成全名
     */
    public void generateFullName() {
        StringBuilder fullNameBuilder = new StringBuilder();
        if (firstName != null && !firstName.trim().isEmpty()) {
            fullNameBuilder.append(firstName.trim());
        }
        if (lastName != null && !lastName.trim().isEmpty()) {
            if (fullNameBuilder.length() > 0) {
                fullNameBuilder.append(" ");
            }
            fullNameBuilder.append(lastName.trim());
        }
        this.fullName = fullNameBuilder.toString();
    }
    
    @Override
    public String toString() {
        return "UserData{" +
                "mysqlId=" + mysqlId +
                ", mysqlTable='" + mysqlTable + '\'' +
                ", uid='" + uid + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", fullName='" + fullName + '\'' +
                ", gender='" + gender + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
