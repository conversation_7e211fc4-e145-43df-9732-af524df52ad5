package com.example.migration.util;

import com.example.migration.config.MigrationConfig;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * Elasticsearch连接测试工具
 * 用于诊断和测试Elasticsearch连接问题
 */
public class ElasticsearchConnectionTester {
    
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchConnectionTester.class);
    
    /**
     * 测试Elasticsearch连接
     */
    public static void main(String[] args) {
        logger.info("=== Elasticsearch连接测试工具 ===");
        
        try {
            MigrationConfig config = new MigrationConfig();
            
            // 显示配置信息
            printConnectionInfo(config);
            
            // 测试连接
            testConnection(config);
            
        } catch (Exception e) {
            logger.error("连接测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 打印连接信息
     */
    private static void printConnectionInfo(MigrationConfig config) {
        logger.info("连接配置信息:");
        logger.info("  主机: {}", config.getElasticsearchHost());
        logger.info("  端口: {}", config.getElasticsearchPort());
        logger.info("  协议: {}", config.getElasticsearchScheme());
        logger.info("  用户名: {}", config.getElasticsearchUsername());
        logger.info("  跳过SSL验证: {}", config.getBooleanProperty("elasticsearch.ssl.skip-verification", false));
    }
    
    /**
     * 测试连接
     */
    private static void testConnection(MigrationConfig config) {
        logger.info("开始测试连接...");
        
        RestHighLevelClient client = null;
        try {
            // 创建客户端
            client = createTestClient(config);
            
            // 测试ping
            logger.info("执行ping测试...");
            boolean pingResult = client.ping(RequestOptions.DEFAULT);
            
            if (pingResult) {
                logger.info("✅ 连接测试成功！");
                
                // 获取集群信息
                try {
                    logger.info("获取集群信息...");
                    // 这里可以添加更多的测试，比如获取集群健康状态
                    logger.info("✅ 集群信息获取成功！");
                } catch (Exception e) {
                    logger.warn("⚠️ 获取集群信息失败，但基本连接正常: {}", e.getMessage());
                }
                
            } else {
                logger.error("❌ 连接测试失败：ping返回false");
            }
            
        } catch (Exception e) {
            logger.error("❌ 连接测试失败", e);
            
            // 提供解决建议
            provideSuggestions(e, config);
            
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (Exception e) {
                    logger.warn("关闭客户端时发生错误", e);
                }
            }
        }
    }
    
    /**
     * 创建测试客户端
     */
    private static RestHighLevelClient createTestClient(MigrationConfig config) throws Exception {
        HttpHost httpHost = new HttpHost(
                config.getElasticsearchHost(),
                config.getElasticsearchPort(),
                config.getElasticsearchScheme()
        );
        
        RestClientBuilder builder = RestClient.builder(httpHost);
        
        // 配置认证和SSL
        configureClientBuilder(builder, config);
        
        return new RestHighLevelClient(builder);
    }
    
    /**
     * 配置客户端构建器
     */
    private static void configureClientBuilder(RestClientBuilder builder, MigrationConfig config) {
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            try {
                // 配置认证
                String username = config.getElasticsearchUsername();
                String password = config.getElasticsearchPassword();
                
                if (username != null && !username.trim().isEmpty() && 
                    password != null && !password.trim().isEmpty()) {
                    
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY,
                            new UsernamePasswordCredentials(username, password));
                    
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    logger.info("已配置认证信息");
                }
                
                // 配置SSL
                String scheme = config.getElasticsearchScheme();
                if ("https".equalsIgnoreCase(scheme)) {
                    boolean skipSslVerification = config.getBooleanProperty("elasticsearch.ssl.skip-verification", false);
                    
                    if (skipSslVerification) {
                        logger.warn("⚠️ 已禁用SSL证书验证");
                        
                        SSLContext sslContext = SSLContexts.custom()
                                .loadTrustMaterial(null, new TrustAllStrategy())
                                .build();
                        
                        httpClientBuilder
                                .setSSLContext(sslContext)
                                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);
                    } else {
                        logger.info("使用标准SSL验证");
                    }
                }
                
            } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
                logger.error("配置SSL时发生错误", e);
                throw new RuntimeException("SSL配置失败", e);
            }
            
            return httpClientBuilder;
        });
        
        // 设置超时
        builder.setRequestConfigCallback(requestConfigBuilder ->
                requestConfigBuilder
                        .setConnectTimeout(10000)
                        .setSocketTimeout(30000));
    }
    
    /**
     * 提供解决建议
     */
    private static void provideSuggestions(Exception e, MigrationConfig config) {
        logger.info("=== 解决建议 ===");
        
        String errorMessage = e.getMessage();
        String scheme = config.getElasticsearchScheme();
        
        if (errorMessage != null && errorMessage.contains("SSLHandshakeException")) {
            logger.info("🔧 SSL证书问题解决方案:");
            logger.info("1. 开发环境解决方案（推荐）:");
            logger.info("   在配置文件中添加: elasticsearch.ssl.skip-verification=true");
            logger.info("2. 生产环境解决方案:");
            logger.info("   配置正确的SSL证书路径");
            logger.info("3. 简单解决方案:");
            logger.info("   如果可能，改用HTTP连接: elasticsearch.scheme=http");
            
        } else if (errorMessage != null && errorMessage.contains("Connection refused")) {
            logger.info("🔧 连接被拒绝解决方案:");
            logger.info("1. 检查Elasticsearch是否正在运行");
            logger.info("2. 验证主机地址和端口号");
            logger.info("3. 检查防火墙设置");
            
        } else if (errorMessage != null && errorMessage.contains("401")) {
            logger.info("🔧 认证失败解决方案:");
            logger.info("1. 检查用户名和密码是否正确");
            logger.info("2. 验证用户是否有相应权限");
            
        } else {
            logger.info("🔧 通用解决方案:");
            logger.info("1. 检查网络连接");
            logger.info("2. 验证Elasticsearch服务状态");
            logger.info("3. 查看Elasticsearch日志");
        }
        
        logger.info("详细配置指南请参考: SSL_CONFIGURATION_GUIDE.md");
    }
}
