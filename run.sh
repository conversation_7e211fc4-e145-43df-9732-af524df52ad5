#!/bin/bash

# MySQL到Elasticsearch数据迁移工具启动脚本 (Linux/Mac)
# 使用方法：chmod +x run.sh && ./run.sh

echo "========================================"
echo "MySQL到Elasticsearch数据迁移工具"
echo "版本: 1.0.0"
echo "========================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java运行环境"
    echo "请确保已安装Java 8或更高版本，并配置了PATH环境变量"
    exit 1
fi

# 显示Java版本
echo "Java版本信息:"
java -version
echo

# 检查配置文件
if [ ! -f "application.properties" ]; then
    echo "警告: 未找到配置文件 application.properties"
    if [ -f "application.properties.example" ]; then
        echo "发现示例配置文件，正在复制..."
        cp "application.properties.example" "application.properties"
        echo
        echo "请编辑 application.properties 文件，配置数据库连接信息"
        echo "配置完成后重新运行此脚本"
        exit 1
    else
        echo "错误: 未找到配置文件，请创建 application.properties"
        exit 1
    fi
fi

# 检查JAR文件
JAR_FILE="target/mysql-to-elasticsearch-migration-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: 未找到JAR文件 $JAR_FILE"
    echo "请先运行 mvn clean package 编译项目"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 设置JVM参数
JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication"

echo "正在启动数据迁移工具..."
echo "JAR文件: $JAR_FILE"
echo "JVM参数: $JAVA_OPTS"
echo

# 启动应用程序
java $JAVA_OPTS -jar "$JAR_FILE"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "数据迁移成功完成！"
    echo "========================================"
else
    echo
    echo "========================================"
    echo "数据迁移失败，错误代码: $?"
    echo "请检查日志文件获取详细错误信息"
    echo "========================================"
    exit 1
fi
