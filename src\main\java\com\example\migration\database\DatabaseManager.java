package com.example.migration.database;

import com.example.migration.config.MigrationConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接管理类
 * 负责管理MySQL数据库连接池
 */
public class DatabaseManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    
    private HikariDataSource dataSource;
    private MigrationConfig config;
    
    public DatabaseManager(MigrationConfig config) {
        this.config = config;
        initializeDataSource();
    }
    
    /**
     * 初始化数据源
     */
    private void initializeDataSource() {
        try {
            logger.info("正在初始化数据库连接池...");
            
            HikariConfig hikariConfig = new HikariConfig();
            
            // 基本连接配置
            hikariConfig.setJdbcUrl(config.getMysqlUrl());
            hikariConfig.setUsername(config.getMysqlUsername());
            hikariConfig.setPassword(config.getMysqlPassword());
            hikariConfig.setDriverClassName(config.getMysqlDriver());
            
            // 连接池配置（优化大数据量处理）
            hikariConfig.setMaximumPoolSize(config.getMaximumPoolSize());
            hikariConfig.setMinimumIdle(config.getMinimumIdle());
            hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
            hikariConfig.setIdleTimeout(config.getIdleTimeout());
            hikariConfig.setMaxLifetime(config.getMaxLifetime());

            // 新增配置项
            hikariConfig.setKeepaliveTime(config.getIntProperty("mysql.pool.keepalive-time", 30000));
            hikariConfig.setValidationTimeout(config.getIntProperty("mysql.pool.validation-timeout", 5000));
            hikariConfig.setLeakDetectionThreshold(config.getIntProperty("mysql.pool.leak-detection-threshold", 60000));
            
            // 连接池名称
            hikariConfig.setPoolName("MigrationPool");
            
            // 连接测试查询和验证配置
            hikariConfig.setConnectionTestQuery("SELECT 1");

            // 连接验证配置
            boolean testOnBorrow = config.getBooleanProperty("migration.connection.test-on-borrow", true);
            boolean testWhileIdle = config.getBooleanProperty("migration.connection.test-while-idle", true);

            if (testOnBorrow) {
                hikariConfig.addDataSourceProperty("testOnBorrow", "true");
            }
            if (testWhileIdle) {
                hikariConfig.addDataSourceProperty("testWhileIdle", "true");
                hikariConfig.addDataSourceProperty("timeBetweenEvictionRunsMillis", "30000");
            }
            
            // 其他优化配置
            hikariConfig.setLeakDetectionThreshold(60000); // 60秒泄漏检测
            hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
            hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
            hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
            hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
            hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
            hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
            hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
            hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
            hikariConfig.addDataSourceProperty("maintainTimeStats", "false");
            
            this.dataSource = new HikariDataSource(hikariConfig);
            
            // 测试连接
            testConnection();
            
            logger.info("数据库连接池初始化成功");
            
        } catch (Exception e) {
            logger.error("数据库连接池初始化失败", e);
            throw new RuntimeException("数据库连接池初始化失败", e);
        }
    }
    
    /**
     * 测试数据库连接
     */
    private void testConnection() {
        try (Connection connection = getConnection()) {
            if (connection.isValid(5)) {
                logger.info("数据库连接测试成功");
            } else {
                throw new SQLException("数据库连接无效");
            }
        } catch (SQLException e) {
            logger.error("数据库连接测试失败", e);
            throw new RuntimeException("数据库连接测试失败", e);
        }
    }
    
    /**
     * 获取数据库连接（带重试机制）
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("数据源未初始化");
        }

        int maxAttempts = config.getIntProperty("migration.retry.max-attempts", 3);
        int delaySeconds = config.getIntProperty("migration.retry.delay-seconds", 5);

        SQLException lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                Connection connection = dataSource.getConnection();

                // 验证连接有效性
                if (connection.isValid(5)) {
                    if (attempt > 1) {
                        logger.info("连接重试成功，第 {} 次尝试", attempt);
                    }
                    return connection;
                } else {
                    connection.close();
                    throw new SQLException("获取的连接无效");
                }

            } catch (SQLException e) {
                lastException = e;
                logger.warn("获取数据库连接失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < maxAttempts) {
                    try {
                        Thread.sleep(delaySeconds * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new SQLException("连接重试被中断", ie);
                    }
                }
            }
        }

        throw new SQLException("经过 " + maxAttempts + " 次尝试后仍无法获取数据库连接", lastException);
    }

    /**
     * 获取数据库连接（不重试，用于内部测试）
     */
    public Connection getConnectionDirect() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("数据源未初始化");
        }
        return dataSource.getConnection();
    }
    
    /**
     * 获取数据源
     */
    public DataSource getDataSource() {
        return dataSource;
    }
    
    /**
     * 检查表是否存在
     */
    public boolean tableExists(String tableName) {
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
        
        try (Connection connection = getConnection();
             java.sql.PreparedStatement statement = connection.prepareStatement(sql)) {
            
            statement.setString(1, config.getMysqlDatabase());
            statement.setString(2, tableName);
            
            try (java.sql.ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            logger.error("检查表 {} 是否存在时发生错误", tableName, e);
        }
        
        return false;
    }
    
    /**
     * 获取表的记录总数
     */
    public long getTableRowCount(String tableName) {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        
        try (Connection connection = getConnection();
             java.sql.PreparedStatement statement = connection.prepareStatement(sql);
             java.sql.ResultSet resultSet = statement.executeQuery()) {
            
            if (resultSet.next()) {
                return resultSet.getLong(1);
            }
            
        } catch (SQLException e) {
            logger.error("获取表 {} 记录数时发生错误", tableName, e);
        }
        
        return 0;
    }
    
    /**
     * 获取连接池状态信息
     */
    public void printPoolStatus() {
        if (dataSource != null) {
            logger.info("=== 连接池状态 ===");
            logger.info("活跃连接数: {}", dataSource.getHikariPoolMXBean().getActiveConnections());
            logger.info("空闲连接数: {}", dataSource.getHikariPoolMXBean().getIdleConnections());
            logger.info("总连接数: {}", dataSource.getHikariPoolMXBean().getTotalConnections());
            logger.info("等待连接的线程数: {}", dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
            logger.info("================");
        }
    }

    /**
     * 检查连接池健康状态
     */
    public boolean isHealthy() {
        if (dataSource == null || dataSource.isClosed()) {
            return false;
        }

        try (Connection conn = getConnectionDirect()) {
            return conn.isValid(5);
        } catch (SQLException e) {
            logger.warn("连接池健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 强制刷新连接池
     */
    public void refreshPool() {
        if (dataSource != null) {
            logger.info("正在刷新连接池...");
            dataSource.getHikariPoolMXBean().softEvictConnections();
            logger.info("连接池刷新完成");
        }
    }
    
    /**
     * 关闭数据源
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            logger.info("正在关闭数据库连接池...");
            dataSource.close();
            logger.info("数据库连接池已关闭");
        }
    }
    
    /**
     * 验证所有分表是否存在
     */
    public void validateTables() {
        logger.info("开始验证分表是否存在...");
        
        int tableCount = config.getTableCount();
        String tablePrefix = config.getTablePrefix();
        int missingTables = 0;
        
        for (int i = 0; i < tableCount; i++) {
            String tableName = tablePrefix + i;
            if (!tableExists(tableName)) {
                logger.warn("表 {} 不存在", tableName);
                missingTables++;
            }
        }
        
        if (missingTables > 0) {
            logger.warn("发现 {} 张表不存在，总共应该有 {} 张表", missingTables, tableCount);
        } else {
            logger.info("所有 {} 张分表验证通过", tableCount);
        }
    }
    
    /**
     * 统计所有分表的总记录数
     */
    public long getTotalRowCount() {
        logger.info("开始统计所有分表的总记录数...");
        
        long totalCount = 0;
        int tableCount = config.getTableCount();
        String tablePrefix = config.getTablePrefix();
        
        for (int i = 0; i < tableCount; i++) {
            String tableName = tablePrefix + i;
            if (tableExists(tableName)) {
                long rowCount = getTableRowCount(tableName);
                totalCount += rowCount;
                logger.debug("表 {} 记录数: {}", tableName, rowCount);
            }
        }
        
        logger.info("所有分表总记录数: {}", totalCount);
        return totalCount;
    }
}
