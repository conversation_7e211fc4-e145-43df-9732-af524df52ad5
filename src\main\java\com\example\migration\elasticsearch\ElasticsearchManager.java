package com.example.migration.elasticsearch;

import com.example.migration.config.MigrationConfig;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Elasticsearch客户端管理类
 * 负责管理Elasticsearch连接和索引操作
 */
public class ElasticsearchManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchManager.class);
    
    private RestHighLevelClient client;
    private MigrationConfig config;
    
    public ElasticsearchManager(MigrationConfig config) {
        this.config = config;
        initializeClient();
    }
    
    /**
     * 初始化Elasticsearch客户端
     */
    private void initializeClient() {
        try {
            logger.info("正在初始化Elasticsearch客户端...");
            
            HttpHost httpHost = new HttpHost(
                    config.getElasticsearchHost(),
                    config.getElasticsearchPort(),
                    config.getElasticsearchScheme()
            );
            
            RestClientBuilder builder = RestClient.builder(httpHost);
            
            // 如果配置了用户名和密码，则设置认证
            String username = config.getElasticsearchUsername();
            String password = config.getElasticsearchPassword();
            
            if (username != null && !username.trim().isEmpty() && 
                password != null && !password.trim().isEmpty()) {
                
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(username, password));
                
                builder.setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
                
                logger.info("已配置Elasticsearch认证信息");
            }
            
            // 设置连接超时和socket超时
            builder.setRequestConfigCallback(requestConfigBuilder ->
                    requestConfigBuilder
                            .setConnectTimeout(5000)
                            .setSocketTimeout(60000));
            
            this.client = new RestHighLevelClient(builder);
            
            // 测试连接
            testConnection();
            
            logger.info("Elasticsearch客户端初始化成功");
            
        } catch (Exception e) {
            logger.error("Elasticsearch客户端初始化失败", e);
            throw new RuntimeException("Elasticsearch客户端初始化失败", e);
        }
    }
    
    /**
     * 测试Elasticsearch连接
     */
    private void testConnection() {
        try {
            boolean isConnected = client.ping(RequestOptions.DEFAULT);
            if (isConnected) {
                logger.info("Elasticsearch连接测试成功");
            } else {
                throw new RuntimeException("Elasticsearch连接测试失败");
            }
        } catch (IOException e) {
            logger.error("Elasticsearch连接测试失败", e);
            throw new RuntimeException("Elasticsearch连接测试失败", e);
        }
    }
    
    /**
     * 检查索引是否存在
     */
    public boolean indexExists(String indexName) {
        try {
            GetIndexRequest request = new GetIndexRequest(indexName);
            return client.indices().exists(request, RequestOptions.DEFAULT);
        } catch (Exception e){
            logger.error("检查索引 {} 是否存在时发生错误", indexName, e);
            return false;
        }
    }
    
    /**
     * 创建索引
     */
    public boolean createIndex(String indexName) {
        try {
            if (indexExists(indexName)) {
                logger.info("索引 {} 已存在，跳过创建", indexName);
                return true;
            }
            
            CreateIndexRequest request = new CreateIndexRequest(indexName);
            
            // 设置索引映射
            String mapping = getIndexMapping();
            request.mapping(mapping, XContentType.JSON);
            
            // 设置索引设置
            String settings = getIndexSettings();
            request.settings(settings, XContentType.JSON);
            
            CreateIndexResponse response = client.indices().create(request, RequestOptions.DEFAULT);
            
            if (response.isAcknowledged()) {
                logger.info("索引 {} 创建成功", indexName);
                return true;
            } else {
                logger.error("索引 {} 创建失败", indexName);
                return false;
            }
            
        } catch (IOException e) {
            logger.error("创建索引 {} 时发生错误", indexName, e);
            return false;
        }
    }
    
    /**
     * 删除索引
     */
    public boolean deleteIndex(String indexName) {
        try {
            if (!indexExists(indexName)) {
                logger.info("索引 {} 不存在，跳过删除", indexName);
                return true;
            }
            
            DeleteIndexRequest request = new DeleteIndexRequest(indexName);
            AcknowledgedResponse response = client.indices().delete(request, RequestOptions.DEFAULT);
            
            if (response.isAcknowledged()) {
                logger.info("索引 {} 删除成功", indexName);
                return true;
            } else {
                logger.error("索引 {} 删除失败", indexName);
                return false;
            }
            
        } catch (IOException e) {
            logger.error("删除索引 {} 时发生错误", indexName, e);
            return false;
        }
    }
    
    /**
     * 批量索引文档
     */
    public boolean bulkIndex(BulkRequest bulkRequest) {
        try {
            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            
            if (bulkResponse.hasFailures()) {
                logger.error("批量索引存在失败项: {}", bulkResponse.buildFailureMessage());
                return false;
            }
            
            return true;
            
        } catch (IOException e) {
            logger.error("批量索引时发生错误", e);
            return false;
        }
    }
    
    /**
     * 创建索引请求
     */
    public IndexRequest createIndexRequest(String indexName, String documentId, String jsonSource) {
        return new IndexRequest(indexName)
                .id(documentId)
                .source(jsonSource, XContentType.JSON);
    }
    
    /**
     * 获取索引映射配置
     */
    private String getIndexMapping() {
        return "{\n" +
                "  \"properties\": {\n" +
                "    \"birth_date\": {\n" +
                "      \"type\": \"date\",\n" +
                "      \"format\": \"yyyy-MM-dd||epoch_millis\"\n" +
                "    },\n" +
                "    \"birth_place\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"email\": {\n" +
                "      \"type\": \"keyword\",\n" +
                "      \"ignore_above\": 256\n" +
                "    },\n" +
                "    \"first_name\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"copy_to\": [\n" +
                "        \"full_name\"\n" +
                "      ]\n" +
                "    },\n" +
                "    \"full_name\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"fields\": {\n" +
                "        \"keyword\": {\n" +
                "          \"type\": \"keyword\",\n" +
                "          \"ignore_above\": 256\n" +
                "        },\n" +
                "        \"standard\": {\n" +
                "          \"type\": \"text\",\n" +
                "          \"analyzer\": \"standard\"\n" +
                "        }\n" +
                "      },\n" +
                "      \"analyzer\": \"name_analyzer\"\n" +
                "    },\n" +
                "    \"gender\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"join_date\": {\n" +
                "      \"type\": \"date\",\n" +
                "      \"format\": \"yyyy-MM-dd||epoch_millis\"\n" +
                "    },\n" +
                "    \"last_name\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"copy_to\": [\n" +
                "        \"full_name\"\n" +
                "      ]\n" +
                "    },\n" +
                "    \"marital_status\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"mysql_id\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"mysql_table\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"name_initials\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"phone_number\": {\n" +
                "      \"type\": \"keyword\",\n" +
                "      \"ignore_above\": 32\n" +
                "    },\n" +
                "    \"uid\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"work_place\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"fields\": {\n" +
                "        \"keyword\": {\n" +
                "          \"type\": \"keyword\"\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"workplace_city\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 获取索引设置配置
     */
    private String getIndexSettings() {
        return "{\n" +
                "  \"number_of_shards\": 3,\n" +
                "  \"number_of_replicas\": 1,\n" +
                "  \"analysis\": {\n" +
                "    \"analyzer\": {\n" +
                "      \"name_analyzer\": {\n" +
                "        \"type\": \"custom\",\n" +
                "        \"tokenizer\": \"standard\",\n" +
                "        \"filter\": [\"lowercase\", \"trim\"]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 获取客户端实例
     */
    public RestHighLevelClient getClient() {
        return client;
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        if (client != null) {
            try {
                logger.info("正在关闭Elasticsearch客户端...");
                client.close();
                logger.info("Elasticsearch客户端已关闭");
            } catch (IOException e) {
                logger.error("关闭Elasticsearch客户端时发生错误", e);
            }
        }
    }
}
