-- MySQL服务器优化配置脚本
-- 用于解决大数据量迁移时的连接超时问题

-- ================================
-- 连接和超时配置优化
-- ================================

-- 设置等待超时时间为8小时（默认值，但确保设置正确）
SET GLOBAL wait_timeout = 28800;
SET SESSION wait_timeout = 28800;

-- 设置交互式超时时间
SET GLOBAL interactive_timeout = 28800;
SET SESSION interactive_timeout = 28800;

-- 设置网络读取超时时间（秒）
SET GLOBAL net_read_timeout = 600;
SET SESSION net_read_timeout = 600;

-- 设置网络写入超时时间（秒）
SET GLOBAL net_write_timeout = 600;
SET SESSION net_write_timeout = 600;

-- ================================
-- 连接数配置优化
-- ================================

-- 设置最大连接数
SET GLOBAL max_connections = 500;

-- 设置最大用户连接数
SET GLOBAL max_user_connections = 450;

-- 设置连接错误最大次数
SET GLOBAL max_connect_errors = 100000;

-- ================================
-- 数据包和缓冲区配置
-- ================================

-- 设置最大数据包大小为1GB
SET GLOBAL max_allowed_packet = 1073741824;
SET SESSION max_allowed_packet = 1073741824;

-- 设置排序缓冲区大小
SET GLOBAL sort_buffer_size = 2097152;
SET SESSION sort_buffer_size = 2097152;

-- 设置读取缓冲区大小
SET GLOBAL read_buffer_size = 131072;
SET SESSION read_buffer_size = 131072;

-- 设置随机读取缓冲区大小
SET GLOBAL read_rnd_buffer_size = 262144;
SET SESSION read_rnd_buffer_size = 262144;

-- ================================
-- InnoDB配置优化
-- ================================

-- 设置InnoDB缓冲池大小（根据服务器内存调整）
-- SET GLOBAL innodb_buffer_pool_size = 2147483648; -- 2GB，需要重启MySQL

-- 设置InnoDB日志文件大小
-- SET GLOBAL innodb_log_file_size = 268435456; -- 256MB，需要重启MySQL

-- 设置InnoDB刷新日志的频率
SET GLOBAL innodb_flush_log_at_trx_commit = 2;

-- 设置InnoDB锁等待超时时间
SET GLOBAL innodb_lock_wait_timeout = 120;

-- ================================
-- 查询缓存配置（如果启用）
-- ================================

-- 设置查询缓存大小
SET GLOBAL query_cache_size = 67108864; -- 64MB

-- 设置查询缓存类型
SET GLOBAL query_cache_type = 1;

-- ================================
-- 临时表配置
-- ================================

-- 设置临时表最大大小
SET GLOBAL tmp_table_size = 67108864; -- 64MB
SET SESSION tmp_table_size = 67108864;

-- 设置最大堆表大小
SET GLOBAL max_heap_table_size = 67108864; -- 64MB
SET SESSION max_heap_table_size = 67108864;

-- ================================
-- 线程配置
-- ================================

-- 设置线程缓存大小
SET GLOBAL thread_cache_size = 50;

-- 设置表打开缓存
SET GLOBAL table_open_cache = 2000;

-- ================================
-- 显示当前配置
-- ================================

SELECT 'Current MySQL Configuration' as Info;

SELECT 
    'Connection Settings' as Category,
    @@wait_timeout as wait_timeout,
    @@interactive_timeout as interactive_timeout,
    @@net_read_timeout as net_read_timeout,
    @@net_write_timeout as net_write_timeout,
    @@max_connections as max_connections,
    @@max_user_connections as max_user_connections;

SELECT 
    'Buffer Settings' as Category,
    @@max_allowed_packet as max_allowed_packet,
    @@sort_buffer_size as sort_buffer_size,
    @@read_buffer_size as read_buffer_size,
    @@read_rnd_buffer_size as read_rnd_buffer_size;

SELECT 
    'InnoDB Settings' as Category,
    @@innodb_buffer_pool_size as innodb_buffer_pool_size,
    @@innodb_flush_log_at_trx_commit as innodb_flush_log_at_trx_commit,
    @@innodb_lock_wait_timeout as innodb_lock_wait_timeout;

SELECT 
    'Cache Settings' as Category,
    @@query_cache_size as query_cache_size,
    @@query_cache_type as query_cache_type,
    @@tmp_table_size as tmp_table_size,
    @@max_heap_table_size as max_heap_table_size;

-- ================================
-- 检查当前连接状态
-- ================================

SELECT 'Current Connection Status' as Info;

SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Max_used_connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Threads_running';
SHOW STATUS LIKE 'Aborted_connects';
SHOW STATUS LIKE 'Aborted_clients';

-- ================================
-- 检查当前进程列表
-- ================================

SELECT 'Current Process List' as Info;
SHOW PROCESSLIST;

-- ================================
-- 使用说明
-- ================================

/*
使用说明：

1. 连接到MySQL服务器：
   mysql -u root -p

2. 执行此脚本：
   source mysql-optimization.sql;
   或者
   mysql -u root -p < mysql-optimization.sql

3. 对于需要重启的配置项，请在my.cnf或my.ini中添加：
   [mysqld]
   innodb_buffer_pool_size = 2G
   innodb_log_file_size = 256M
   max_allowed_packet = 1G
   wait_timeout = 28800
   interactive_timeout = 28800
   max_connections = 500

4. 重启MySQL服务：
   # Linux
   sudo systemctl restart mysql
   # 或
   sudo service mysql restart
   
   # Windows
   net stop mysql
   net start mysql

5. 验证配置：
   重新运行此脚本查看配置是否生效

注意事项：
- 请根据服务器实际内存大小调整innodb_buffer_pool_size
- 建议在非生产环境先测试这些配置
- 某些配置需要重启MySQL服务才能生效
- 监控服务器性能，根据实际情况调整参数
*/
