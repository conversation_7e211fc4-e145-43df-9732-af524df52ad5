package com.example.migration.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 日期解析测试工具
 * 用于测试和验证日期格式解析功能
 */
public class DateParsingTester {
    
    private static final Logger logger = LoggerFactory.getLogger(DateParsingTester.class);
    
    // 测试用的日期格式化器（与UserDataDao中的保持一致）
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            // 标准ISO格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            
            // 美式日期格式（M/d/yyyy）
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm:ss a", Locale.US),
            DateTimeFormatter.ofPattern("M/d/yyyy"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm:ss a", Locale.US),
            
            // 其他常见格式
            DateTimeFormatter.ofPattern("d/M/yyyy"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("M-d-yyyy"),
            DateTimeFormatter.ofPattern("MM-dd-yyyy"),
            DateTimeFormatter.ofPattern("d-M-yyyy"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy"),
            
            // 带时间的格式
            DateTimeFormatter.ofPattern("M/d/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("d/M/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy H:mm:ss"),
            
            // 12小时制格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("yyyy/MM/dd h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm a", Locale.ENGLISH),
            
            // 紧凑格式
            DateTimeFormatter.ofPattern("yyyyMMdd"),
            DateTimeFormatter.ofPattern("yyMMdd"),
            DateTimeFormatter.ofPattern("MMddyyyy"),
            DateTimeFormatter.ofPattern("ddMMyyyy")
    };
    
    // 无效日期值列表
    private static final String[] INVALID_DATE_VALUES = {
            "1/1/0001 12:00:00 AM",
            "1/1/0001",
            "0001-01-01",
            "0000-00-00",
            "1900-01-01 00:00:00",
            "null",
            "NULL",
            "",
            " "
    };
    
    public static void main(String[] args) {
        logger.info("=== 日期解析测试工具 ===");
        
        // 测试用例
        List<String> testDates = Arrays.asList(
                // 问题中提到的格式
                "5/6/2019 12:00:00 AM",
                "01/04/1996",
                "1/1/0001 12:00:00 AM",
                
                // 其他常见格式
                "2023-12-25",
                "2023/12/25",
                "12/25/2023",
                "25/12/2023",
                "12-25-2023",
                "25-12-2023",
                "2023-12-25 14:30:00",
                "12/25/2023 2:30:00 PM",
                "25/12/2023 14:30:00",
                "20231225",
                "231225",
                "12252023",
                "25122023",
                
                // 边界情况
                "1/1/2000",
                "12/31/1999",
                "2/29/2020", // 闰年
                "2/28/2021", // 非闰年
                
                // 无效值
                "1/1/0001 12:00:00 AM",
                "0001-01-01",
                "null",
                "",
                "invalid_date",
                "13/45/2023", // 无效月日
                
                // 时间戳
                "1640995200", // 2022-01-01 00:00:00 UTC (秒)
                "1640995200000" // 2022-01-01 00:00:00 UTC (毫秒)
        );
        
        logger.info("开始测试 {} 个日期字符串...", testDates.size());
        
        int successCount = 0;
        int failureCount = 0;
        int invalidCount = 0;
        
        for (String dateString : testDates) {
            logger.info("测试日期字符串: '{}'", dateString);
            
            LocalDate result = parseDate(dateString);
            if (result != null) {
                logger.info("  ✅ 解析成功: {} -> {}", dateString, result);
                successCount++;
            } else if (isInvalidDate(dateString)) {
                logger.info("  ⚠️ 识别为无效日期: {}", dateString);
                invalidCount++;
            } else {
                logger.warn("  ❌ 解析失败: {}", dateString);
                failureCount++;
            }
            
            System.out.println(); // 空行分隔
        }
        
        // 输出统计结果
        logger.info("=== 测试结果统计 ===");
        logger.info("总测试数: {}", testDates.size());
        logger.info("解析成功: {}", successCount);
        logger.info("识别为无效: {}", invalidCount);
        logger.info("解析失败: {}", failureCount);
        logger.info("成功率: {:.2f}%", (double) successCount / testDates.size() * 100);
        
        if (failureCount > 0) {
            logger.warn("存在解析失败的日期，请检查日期格式配置");
        } else {
            logger.info("所有有效日期均解析成功！");
        }
    }
    
    /**
     * 解析日期字符串（与UserDataDao中的逻辑保持一致）
     */
    public static LocalDate parseDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        
        String cleanDateString = dateString.trim();
        
        if (isInvalidDate(cleanDateString)) {
            return null;
        }
        
        cleanDateString = preprocessDateString(cleanDateString);
        
        LocalDate result = tryParseAsDate(cleanDateString);
        if (result != null) {
            return result;
        }
        
        result = tryParseAsDateTime(cleanDateString);
        if (result != null) {
            return result;
        }
        
        result = tryParseAsTimestamp(cleanDateString);
        if (result != null) {
            return result;
        }
        
        return null;
    }
    
    public static boolean isInvalidDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return true;
        }
        
        String normalized = dateString.trim().toLowerCase();
        for (String invalidValue : INVALID_DATE_VALUES) {
            if (invalidValue.toLowerCase().equals(normalized)) {
                return true;
            }
        }
        
        if (normalized.startsWith("0001") || normalized.startsWith("1/1/0001") || 
            normalized.startsWith("01/01/0001") || normalized.contains("0001-01-01")) {
            return true;
        }
        
        return false;
    }
    
    private static String preprocessDateString(String dateString) {
        String processed = dateString.trim();
        processed = processed.replaceAll("(?i)\\s+am\\b", " AM");
        processed = processed.replaceAll("(?i)\\s+pm\\b", " PM");
        processed = processed.replaceAll("\\s+", " ");
        processed = processed.replace("'", "");
        processed = processed.replace("\"", "");
        return processed;
    }
    
    private static LocalDate tryParseAsDate(String dateString) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                String pattern = formatter.toString();
                if (pattern.contains("H") || pattern.contains("h") || pattern.contains("a")) {
                    continue;
                }
                return LocalDate.parse(dateString, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试
            }
        }
        return null;
    }
    
    private static LocalDate tryParseAsDateTime(String dateString) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                String pattern = formatter.toString();
                if (!pattern.contains("H") && !pattern.contains("h") && !pattern.contains("a")) {
                    continue;
                }
                LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
                return dateTime.toLocalDate();
            } catch (DateTimeParseException e) {
                // 继续尝试
            }
        }
        return null;
    }
    
    private static LocalDate tryParseAsTimestamp(String dateString) {
        try {
            long timestamp = Long.parseLong(dateString);
            if (timestamp < 0) {
                return null;
            }
            
            if (timestamp > 1000000000000L) {
                return LocalDate.ofEpochDay(timestamp / (1000 * 60 * 60 * 24));
            } else if (timestamp > 1000000000L) {
                return LocalDate.ofEpochDay(timestamp / (60 * 60 * 24));
            } else {
                return LocalDate.ofEpochDay(timestamp);
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
