# MySQL数据库配置
mysql.host=localhost
mysql.port=3306
mysql.database=facebook_db
mysql.username=root
mysql.password=root
mysql.driver=com.mysql.cj.jdbc.Driver

# 连接池配置
mysql.pool.maximum-pool-size=20
mysql.pool.minimum-idle=5
mysql.pool.connection-timeout=30000
mysql.pool.idle-timeout=600000
mysql.pool.max-lifetime=1800000

# Elasticsearch配置
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=3tRW66trZKNOqMJitmcY
elasticsearch.index.name=facebook_user

# SSL配置（当scheme=https时生效）
# 是否跳过SSL证书验证（仅开发环境使用，生产环境请设为false）
elasticsearch.ssl.skip-verification=true
# SSL证书路径（可选，用于自定义证书）
elasticsearch.ssl.certificate.path=
# SSL密钥路径（可选，用于客户端证书认证）
elasticsearch.ssl.key.path=

# 迁移配置
migration.batch.size=1000
migration.thread.pool.size=10
migration.table.count=200
migration.table.prefix=data_

# 日志配置
logging.level.root=INFO
logging.level.com.example.migration=DEBUG
