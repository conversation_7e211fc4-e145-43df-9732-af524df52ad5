# MySQL数据库配置
mysql.host=localhost
mysql.port=3306
mysql.database=facebook_db
mysql.username=root
mysql.password=root
mysql.driver=com.mysql.cj.jdbc.Driver

# 连接池配置（优化大数据量迁移）
mysql.pool.maximum-pool-size=30
mysql.pool.minimum-idle=10
mysql.pool.connection-timeout=60000
mysql.pool.idle-timeout=300000
mysql.pool.max-lifetime=900000
mysql.pool.keepalive-time=30000
mysql.pool.validation-timeout=5000
mysql.pool.leak-detection-threshold=60000

# Elasticsearch配置
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=http
elasticsearch.username=
elasticsearch.password=
elasticsearch.index.name=facebook_user

# SSL配置（当scheme=https时生效）
# 是否跳过SSL证书验证（仅开发环境使用，生产环境请设为false）
elasticsearch.ssl.skip-verification=true
# SSL证书路径（可选，用于自定义证书）
elasticsearch.ssl.certificate.path=
# SSL密钥路径（可选，用于客户端证书认证）
elasticsearch.ssl.key.path=

# 迁移配置（优化大数据量处理）
migration.batch.size=500
migration.thread.pool.size=8
migration.table.count=200
migration.table.prefix=data_

# 重试和恢复配置
migration.retry.max-attempts=3
migration.retry.delay-seconds=5
migration.connection.test-on-borrow=true
migration.connection.test-while-idle=true

# MySQL服务器优化配置
mysql.server.wait-timeout=28800
mysql.server.interactive-timeout=28800
mysql.server.max-allowed-packet=1073741824

# 日志配置
logging.level.root=INFO
logging.level.com.example.migration=DEBUG
