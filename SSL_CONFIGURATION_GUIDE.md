# Elasticsearch SSL配置指南

本文档详细说明如何解决Elasticsearch SSL连接问题，包括开发环境和生产环境的不同配置方案。

## 🚨 错误分析

### 常见SSL错误
```
javax.net.ssl.SSLHandshakeException: PKIX path building failed: 
sun.security.provider.certpath.SunCertPathBuilderException: 
unable to find valid certification path to requested target
```

**错误原因**：
1. Elasticsearch启用了HTTPS，但使用了自签名证书
2. 客户端无法验证服务器证书的有效性
3. 证书链不完整或CA证书未被信任

## 🛠️ 解决方案

### 方案一：开发环境 - 跳过SSL验证（推荐）

**适用场景**：开发、测试环境

**配置步骤**：

1. **修改配置文件**
```properties
# 启用HTTPS但跳过证书验证
elasticsearch.scheme=https
elasticsearch.ssl.skip-verification=true
```

2. **安全警告**
- ⚠️ 此方案会跳过所有SSL证书验证
- ⚠️ 仅适用于开发环境，生产环境禁止使用
- ⚠️ 可能存在中间人攻击风险

### 方案二：生产环境 - 正确配置SSL证书

**适用场景**：生产环境

#### 2.1 使用受信任的CA证书

```properties
# 使用HTTPS和标准证书验证
elasticsearch.scheme=https
elasticsearch.ssl.skip-verification=false
```

**前提条件**：
- Elasticsearch使用由受信任CA签发的证书
- 证书域名与连接地址匹配
- 证书在有效期内

#### 2.2 使用自定义CA证书

```properties
# 使用自定义CA证书
elasticsearch.scheme=https
elasticsearch.ssl.skip-verification=false
elasticsearch.ssl.certificate.path=/path/to/ca-cert.pem
```

**证书获取方法**：
```bash
# 从Elasticsearch服务器获取证书
openssl s_client -connect your-es-host:9200 -showcerts < /dev/null 2>/dev/null | \
openssl x509 -outform PEM > es-ca-cert.pem
```

#### 2.3 配置客户端证书（双向认证）

```properties
# 双向SSL认证
elasticsearch.scheme=https
elasticsearch.ssl.skip-verification=false
elasticsearch.ssl.client.certificate.path=/path/to/client-cert.pem
elasticsearch.ssl.client.key.path=/path/to/client-key.pem
```

### 方案三：使用HTTP连接（最简单）

**适用场景**：内网环境，安全要求不高

```properties
# 使用HTTP连接
elasticsearch.scheme=http
elasticsearch.port=9200
```

**注意事项**：
- 需要Elasticsearch配置允许HTTP连接
- 数据传输不加密
- 不适用于公网环境

## 🔧 Elasticsearch服务端配置

### 启用HTTP连接（如果需要）

在Elasticsearch配置文件 `elasticsearch.yml` 中：

```yaml
# 允许HTTP连接
xpack.security.http.ssl.enabled: false

# 或者同时支持HTTP和HTTPS
xpack.security.http.ssl.enabled: true
http.port: 9200
transport.port: 9300
```

### 配置自签名证书

```bash
# 生成CA证书
elasticsearch-certutil ca --out elastic-stack-ca.p12 --pass ""

# 生成节点证书
elasticsearch-certutil cert --ca elastic-stack-ca.p12 --out elastic-certificates.p12 --pass ""

# 转换为PEM格式
openssl pkcs12 -in elastic-certificates.p12 -out elastic-cert.pem -nokeys -passin pass:""
openssl pkcs12 -in elastic-certificates.p12 -out elastic-key.pem -nocerts -nodes -passin pass:""
```

## 🚀 快速解决步骤

### 立即解决（开发环境）

1. **修改配置文件**
```properties
elasticsearch.ssl.skip-verification=true
```

2. **重新运行程序**
```bash
mvn clean package
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar
```

### 生产环境部署

1. **获取正确的证书**
2. **配置证书路径**
3. **测试连接**
4. **部署应用**

## 🔍 故障排除

### 检查Elasticsearch状态
```bash
# 检查集群状态
curl -k -u elastic:password https://localhost:9200/_cluster/health

# 检查节点信息
curl -k -u elastic:password https://localhost:9200/_nodes
```

### 验证证书
```bash
# 检查证书有效期
openssl x509 -in certificate.pem -text -noout

# 验证证书链
openssl verify -CAfile ca-cert.pem certificate.pem
```

### 测试连接
```bash
# 测试SSL连接
openssl s_client -connect your-es-host:9200 -servername your-es-host

# 测试HTTP连接
curl -v http://your-es-host:9200/
```

## 📝 配置模板

### 开发环境配置
```properties
# 开发环境 - 跳过SSL验证
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=your_password
elasticsearch.ssl.skip-verification=true
```

### 生产环境配置
```properties
# 生产环境 - 完整SSL验证
elasticsearch.host=prod-es.company.com
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=secure_password
elasticsearch.ssl.skip-verification=false
elasticsearch.ssl.certificate.path=/etc/ssl/certs/es-ca-cert.pem
```

## ⚠️ 安全建议

1. **开发环境**：可以跳过SSL验证以简化开发
2. **测试环境**：建议使用真实的SSL配置进行测试
3. **生产环境**：必须使用正确的SSL证书配置
4. **证书管理**：定期更新证书，监控证书过期时间
5. **访问控制**：配置适当的用户权限和网络访问控制

## 🔗 相关链接

- [Elasticsearch Security Documentation](https://www.elastic.co/guide/en/elasticsearch/reference/current/security-settings.html)
- [SSL/TLS Configuration Guide](https://www.elastic.co/guide/en/elasticsearch/reference/current/ssl-tls.html)
- [Certificate Management](https://www.elastic.co/guide/en/elasticsearch/reference/current/certutil.html)
