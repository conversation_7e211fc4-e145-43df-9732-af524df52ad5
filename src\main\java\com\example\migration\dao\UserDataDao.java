package com.example.migration.dao;

import com.example.migration.model.UserData;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户数据访问对象
 * 负责从MySQL数据库中读取用户数据
 */
public class UserDataDao {
    
    private static final Logger logger = LoggerFactory.getLogger(UserDataDao.class);
    
    private DataSource dataSource;
    
    // 日期格式化器 - 支持多种常见日期格式
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            // 标准ISO格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),

            // 美式日期格式（M/d/yyyy）
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm:ss a", Locale.US),
            DateTimeFormatter.ofPattern("M/d/yyyy"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm:ss a", Locale.US),

            // 其他常见格式
            DateTimeFormatter.ofPattern("d/M/yyyy"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("M-d-yyyy"),
            DateTimeFormatter.ofPattern("MM-dd-yyyy"),
            DateTimeFormatter.ofPattern("d-M-yyyy"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy"),

            // 带时间的格式
            DateTimeFormatter.ofPattern("M/d/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("d/M/yyyy H:mm:ss"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy H:mm:ss"),

            // 12小时制格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("yyyy/MM/dd h:mm:ss a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("M/d/yyyy h:mm a", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("MM/dd/yyyy h:mm a", Locale.ENGLISH),

            // 紧凑格式
            DateTimeFormatter.ofPattern("yyyyMMdd"),
            DateTimeFormatter.ofPattern("yyMMdd"),
            DateTimeFormatter.ofPattern("MMddyyyy"),
            DateTimeFormatter.ofPattern("ddMMyyyy")
    };

    // 无效日期值列表
    private static final String[] INVALID_DATE_VALUES = {
            "1/1/0001 12:00:00 AM",
            "1/1/0001",
            "0001-01-01",
            "0000-00-00",
            "1900-01-01 00:00:00",
            "null",
            "NULL",
            "",
            " "
    };
    
    public UserDataDao(DataSource dataSource) {
        this.dataSource = dataSource;
    }
    
    /**
     * 分页查询指定表的用户数据
     * 
     * @param tableName 表名
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 用户数据列表
     */
    public List<UserData> findByTableWithPagination(String tableName, long offset, int limit) {
        List<UserData> userDataList = new ArrayList<>();
        
        String sql = "SELECT id, phone_number, uid, first_name, last_name, gender, " +
                    "birth_place, workplace_city, marital_status, work_place, " +
                    "join_date, email, birth_date " +
                    "FROM " + tableName + " " +
                    "ORDER BY id " +
                    "LIMIT ? OFFSET ?";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {
            
            statement.setInt(1, limit);
            statement.setLong(2, offset);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    UserData userData = mapResultSetToUserData(resultSet, tableName);
                    if (userData != null) {
                        userDataList.add(userData);
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("查询表 {} 数据时发生错误，offset: {}, limit: {}", tableName, offset, limit, e);
        }
        
        return userDataList;
    }
    
    /**
     * 获取指定表的总记录数
     * 
     * @param tableName 表名
     * @return 记录总数
     */
    public long countByTable(String tableName) {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {
            
            if (resultSet.next()) {
                return resultSet.getLong(1);
            }
            
        } catch (SQLException e) {
            logger.error("统计表 {} 记录数时发生错误", tableName, e);
        }
        
        return 0;
    }
    
    /**
     * 将ResultSet映射为UserData对象
     * 
     * @param resultSet 结果集
     * @param tableName 表名
     * @return UserData对象
     */
    private UserData mapResultSetToUserData(ResultSet resultSet, String tableName) {
        try {
            UserData userData = new UserData();
            
            // 设置MySQL相关字段
            userData.setMysqlId(resultSet.getInt("id"));
            userData.setMysqlTable(tableName);
            
            // 设置基本字段
            userData.setPhoneNumber(resultSet.getString("phone_number"));
            userData.setUid(resultSet.getString("uid"));
            userData.setFirstName(resultSet.getString("first_name"));
            userData.setLastName(resultSet.getString("last_name"));
            userData.setGender(resultSet.getString("gender"));
            userData.setBirthPlace(resultSet.getString("birth_place"));
            userData.setWorkplaceCity(resultSet.getString("workplace_city"));
            userData.setMaritalStatus(resultSet.getString("marital_status"));
            userData.setWorkPlace(resultSet.getString("work_place"));
            userData.setEmail(resultSet.getString("email"));
            
            // 处理日期字段
            userData.setJoinDate(parseDate(resultSet.getString("join_date")));
            userData.setBirthDate(parseDate(resultSet.getString("birth_date")));
            
            // 生成计算字段
            userData.generateFullName();
            userData.generateNameInitials();
            
            return userData;
            
        } catch (SQLException e) {
            logger.error("映射ResultSet到UserData时发生错误", e);
            return null;
        }
    }
    
    /**
     * 解析日期字符串为LocalDate
     * 支持多种日期格式，包括美式格式和12小时制
     *
     * @param dateString 日期字符串
     * @return LocalDate对象，解析失败返回null
     */
    private LocalDate parseDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }

        // 清理日期字符串
        String cleanDateString = dateString.trim();

        // 检查是否为无效日期值
        if (isInvalidDate(cleanDateString)) {
            logger.debug("检测到无效日期值，跳过: {}", dateString);
            return null;
        }

        // 预处理日期字符串
        cleanDateString = preprocessDateString(cleanDateString);

        // 尝试解析为LocalDate（仅日期部分）
        LocalDate result = tryParseAsDate(cleanDateString);
        if (result != null) {
            return result;
        }

        // 尝试解析为LocalDateTime然后提取日期部分
        result = tryParseAsDateTime(cleanDateString);
        if (result != null) {
            return result;
        }

        // 尝试解析为时间戳
        result = tryParseAsTimestamp(cleanDateString);
        if (result != null) {
            return result;
        }

        logger.warn("无法解析日期字符串: '{}' (原始值: '{}')", cleanDateString, dateString);
        return null;
    }

    /**
     * 检查是否为无效日期值
     */
    private boolean isInvalidDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return true;
        }

        String normalized = dateString.trim().toLowerCase();
        for (String invalidValue : INVALID_DATE_VALUES) {
            if (invalidValue.toLowerCase().equals(normalized)) {
                return true;
            }
        }

        // 检查年份是否过小（小于1900年的日期通常是无效的默认值）
        if (normalized.startsWith("0001") || normalized.startsWith("1/1/0001") ||
            normalized.startsWith("01/01/0001") || normalized.contains("0001-01-01")) {
            return true;
        }

        return false;
    }

    /**
     * 预处理日期字符串
     */
    private String preprocessDateString(String dateString) {
        String processed = dateString.trim();

        // 处理AM/PM标记的大小写问题
        processed = processed.replaceAll("(?i)\\s+am\\b", " AM");
        processed = processed.replaceAll("(?i)\\s+pm\\b", " PM");

        // 标准化空格
        processed = processed.replaceAll("\\s+", " ");

        // 处理特殊字符
        processed = processed.replace("'", "");
        processed = processed.replace("\"", "");

        return processed;
    }

    /**
     * 尝试解析为LocalDate
     */
    private LocalDate tryParseAsDate(String dateString) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                // 跳过包含时间部分的格式化器
                String pattern = formatter.toString();
                if (pattern.contains("H") || pattern.contains("h") || pattern.contains("a")) {
                    continue;
                }
                return LocalDate.parse(dateString, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        return null;
    }

    /**
     * 尝试解析为LocalDateTime然后提取日期
     */
    private LocalDate tryParseAsDateTime(String dateString) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                // 只使用包含时间部分的格式化器
                String pattern = formatter.toString();
                if (!pattern.contains("H") && !pattern.contains("h") && !pattern.contains("a")) {
                    continue;
                }

                LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
                return dateTime.toLocalDate();
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        return null;
    }

    /**
     * 尝试解析为时间戳
     */
    private LocalDate tryParseAsTimestamp(String dateString) {
        try {
            long timestamp = Long.parseLong(dateString);

            // 验证时间戳的合理性
            if (timestamp < 0) {
                return null;
            }

            // 判断时间戳格式（秒或毫秒）
            if (timestamp > 1000000000000L) {
                // 毫秒级时间戳
                return LocalDate.ofEpochDay(timestamp / (1000 * 60 * 60 * 24));
            } else if (timestamp > 1000000000L) {
                // 秒级时间戳
                return LocalDate.ofEpochDay(timestamp / (60 * 60 * 24));
            } else {
                // 可能是天数
                return LocalDate.ofEpochDay(timestamp);
            }
        } catch (NumberFormatException e) {
            // 不是时间戳格式
            return null;
        }
    }
    
    /**
     * 批量查询指定表的用户数据
     * 
     * @param tableName 表名
     * @param batchSize 批次大小
     * @return 用户数据批次迭代器
     */
    public UserDataBatchIterator getBatchIterator(String tableName, int batchSize) {
        return new UserDataBatchIterator(this, tableName, batchSize);
    }
    
    /**
     * 用户数据批次迭代器
     * 用于分批次读取大量数据，避免内存溢出
     */
    public static class UserDataBatchIterator {
        private UserDataDao dao;
        private String tableName;
        private int batchSize;
        private long currentOffset;
        private long totalCount;
        private boolean hasNext;
        
        public UserDataBatchIterator(UserDataDao dao, String tableName, int batchSize) {
            this.dao = dao;
            this.tableName = tableName;
            this.batchSize = batchSize;
            this.currentOffset = 0;
            this.totalCount = dao.countByTable(tableName);
            this.hasNext = totalCount > 0;
        }
        
        /**
         * 是否还有下一批数据
         */
        public boolean hasNext() {
            return hasNext && currentOffset < totalCount;
        }
        
        /**
         * 获取下一批数据
         */
        public List<UserData> next() {
            if (!hasNext()) {
                return new ArrayList<>();
            }
            
            List<UserData> batch = dao.findByTableWithPagination(tableName, currentOffset, batchSize);
            currentOffset += batchSize;
            
            if (currentOffset >= totalCount) {
                hasNext = false;
            }
            
            return batch;
        }
        
        /**
         * 获取总记录数
         */
        public long getTotalCount() {
            return totalCount;
        }
        
        /**
         * 获取当前偏移量
         */
        public long getCurrentOffset() {
            return currentOffset;
        }
        
        /**
         * 获取进度百分比
         */
        public double getProgress() {
            if (totalCount == 0) {
                return 100.0;
            }
            return Math.min(100.0, (double) currentOffset / totalCount * 100.0);
        }
    }
}
