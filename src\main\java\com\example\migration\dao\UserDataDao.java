package com.example.migration.dao;

import com.example.migration.model.UserData;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
// 移除了日期相关的导入，因为不再处理日期字段
import java.util.ArrayList;
import java.util.List;

/**
 * 用户数据访问对象
 * 负责从MySQL数据库中读取用户数据
 */
public class UserDataDao {
    
    private static final Logger logger = LoggerFactory.getLogger(UserDataDao.class);
    
    private DataSource dataSource;
    
    // 注意：已移除日期格式化器，因为不再处理 join_date, birth_date 字段
    
    public UserDataDao(DataSource dataSource) {
        this.dataSource = dataSource;
    }
    
    /**
     * 分页查询指定表的用户数据
     * 
     * @param tableName 表名
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 用户数据列表
     */
    public List<UserData> findByTableWithPagination(String tableName, long offset, int limit) {
        List<UserData> userDataList = new ArrayList<>();
        
        String sql = "SELECT id, phone_number, uid, first_name, last_name, gender, " +
                    "birth_place, workplace_city, work_place, email " +
                    "FROM " + tableName + " " +
                    "ORDER BY id " +
                    "LIMIT ? OFFSET ?";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {
            
            statement.setInt(1, limit);
            statement.setLong(2, offset);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    UserData userData = mapResultSetToUserData(resultSet, tableName);
                    if (userData != null) {
                        userDataList.add(userData);
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("查询表 {} 数据时发生错误，offset: {}, limit: {}", tableName, offset, limit, e);
        }
        
        return userDataList;
    }
    
    /**
     * 获取指定表的总记录数
     * 
     * @param tableName 表名
     * @return 记录总数
     */
    public long countByTable(String tableName) {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {
            
            if (resultSet.next()) {
                return resultSet.getLong(1);
            }
            
        } catch (SQLException e) {
            logger.error("统计表 {} 记录数时发生错误", tableName, e);
        }
        
        return 0;
    }
    
    /**
     * 将ResultSet映射为UserData对象
     * 
     * @param resultSet 结果集
     * @param tableName 表名
     * @return UserData对象
     */
    private UserData mapResultSetToUserData(ResultSet resultSet, String tableName) {
        try {
            UserData userData = new UserData();
            
            // 设置MySQL相关字段
            userData.setMysqlId(resultSet.getInt("id"));
            userData.setMysqlTable(tableName);
            
            // 设置基本字段
            userData.setPhoneNumber(resultSet.getString("phone_number"));
            userData.setUid(resultSet.getString("uid"));
            userData.setFirstName(resultSet.getString("first_name"));
            userData.setLastName(resultSet.getString("last_name"));
            userData.setGender(resultSet.getString("gender"));
            userData.setBirthPlace(resultSet.getString("birth_place"));
            userData.setWorkplaceCity(resultSet.getString("workplace_city"));
            userData.setWorkPlace(resultSet.getString("work_place"));
            userData.setEmail(resultSet.getString("email"));
            
            // 生成计算字段
            userData.generateFullName();
            userData.generateNameInitials();
            
            return userData;
            
        } catch (SQLException e) {
            logger.error("映射ResultSet到UserData时发生错误", e);
            return null;
        }
    }
    
    // 注意：已移除所有日期解析相关方法，因为不再处理日期字段
    
    /**
     * 批量查询指定表的用户数据
     * 
     * @param tableName 表名
     * @param batchSize 批次大小
     * @return 用户数据批次迭代器
     */
    public UserDataBatchIterator getBatchIterator(String tableName, int batchSize) {
        return new UserDataBatchIterator(this, tableName, batchSize);
    }
    
    /**
     * 用户数据批次迭代器
     * 用于分批次读取大量数据，避免内存溢出
     */
    public static class UserDataBatchIterator {
        private UserDataDao dao;
        private String tableName;
        private int batchSize;
        private long currentOffset;
        private long totalCount;
        private boolean hasNext;
        
        public UserDataBatchIterator(UserDataDao dao, String tableName, int batchSize) {
            this.dao = dao;
            this.tableName = tableName;
            this.batchSize = batchSize;
            this.currentOffset = 0;
            this.totalCount = dao.countByTable(tableName);
            this.hasNext = totalCount > 0;
        }
        
        /**
         * 是否还有下一批数据
         */
        public boolean hasNext() {
            return hasNext && currentOffset < totalCount;
        }
        
        /**
         * 获取下一批数据
         */
        public List<UserData> next() {
            if (!hasNext()) {
                return new ArrayList<>();
            }
            
            List<UserData> batch = dao.findByTableWithPagination(tableName, currentOffset, batchSize);
            currentOffset += batchSize;
            
            if (currentOffset >= totalCount) {
                hasNext = false;
            }
            
            return batch;
        }
        
        /**
         * 获取总记录数
         */
        public long getTotalCount() {
            return totalCount;
        }
        
        /**
         * 获取当前偏移量
         */
        public long getCurrentOffset() {
            return currentOffset;
        }
        
        /**
         * 获取进度百分比
         */
        public double getProgress() {
            if (totalCount == 0) {
                return 100.0;
            }
            return Math.min(100.0, (double) currentOffset / totalCount * 100.0);
        }
    }
}
