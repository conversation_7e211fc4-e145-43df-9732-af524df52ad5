# Elasticsearch查询功能使用指南

本文档详细介绍如何使用MySQL到Elasticsearch数据迁移工具中的查询功能。

## 🚀 快速开始

### 启动查询模式

#### 方法1：使用启动脚本（推荐）
```bash
# Windows
query.bat

# Linux/Mac
chmod +x query.sh && ./query.sh
```

#### 方法2：直接运行JAR文件
```bash
java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar --query
```

#### 方法3：在IDE中运行
```bash
# 添加程序参数
--query
```

## 📋 查询功能概览

### 支持的查询类型

1. **UID精确查询** - 根据用户唯一标识查询
2. **手机号查询** - 根据手机号精确匹配
3. **姓名模糊查询** - 支持姓、名、全名的模糊搜索
4. **工作地点查询** - 根据工作城市或工作地点搜索
5. **复合条件查询** - 多个字段组合查询
6. **全文搜索** - 在所有字段中搜索关键字

### 查询特性

- ✅ **分页支持** - 支持翻页浏览大量结果
- ✅ **高亮显示** - 匹配的关键字会高亮显示
- ✅ **查询统计** - 显示总数、耗时等统计信息
- ✅ **交互式界面** - 友好的命令行交互界面
- ✅ **错误处理** - 完善的错误提示和处理

## 🔍 详细使用说明

### 1. UID精确查询

**使用场景**：已知用户UID，需要查询完整用户信息

**操作步骤**：
1. 选择菜单选项 `1`
2. 输入UID（如：`user123456`）
3. 查看查询结果

**示例**：
```
请选择查询类型:
1. 根据UID查询
请输入选择 (0-6): 1
请输入UID: user123456

========================================
查询结果
========================================
查询条件: UID精确查询: user123456
总记录数: 1
查询耗时: 15ms
当前页: 1/1
显示记录: 1-1

--- 记录 1 ---
UID: user123456
手机号: 13800138000
姓名: 张三 (张 三)
性别: M
邮箱: <EMAIL>
...
```

### 2. 手机号查询

**使用场景**：根据手机号查找用户信息

**操作步骤**：
1. 选择菜单选项 `2`
2. 输入手机号（如：`13800138000`）
3. 查看查询结果

### 3. 姓名模糊查询

**使用场景**：根据姓名关键字查找用户，支持部分匹配

**操作步骤**：
1. 选择菜单选项 `3`
2. 输入姓名关键字（如：`张`、`张三`、`小明`）
3. 设置分页参数（页码、每页大小）
4. 查看查询结果
5. 可以翻页浏览更多结果

**高级功能**：
- 支持姓氏搜索：输入`张`可以找到所有姓张的用户
- 支持名字搜索：输入`明`可以找到所有名字包含明的用户
- 支持全名搜索：输入`张三`精确匹配

### 4. 工作地点查询

**使用场景**：根据工作城市或工作地点查找用户

**操作步骤**：
1. 选择菜单选项 `4`
2. 输入工作地点关键字（如：`北京`、`阿里巴巴`、`互联网`）
3. 设置分页参数
4. 查看查询结果

**搜索范围**：
- 工作城市（workplace_city）
- 工作地点（work_place）

### 5. 复合条件查询

**使用场景**：需要同时满足多个条件的精确查询

**操作步骤**：
1. 选择菜单选项 `5`
2. 依次输入各个查询条件（留空跳过）：
   - UID
   - 手机号
   - 姓名关键字
   - 性别（M/F）
   - 工作城市
   - 工作地点关键字
   - 邮箱
3. 设置分页参数
4. 查看查询结果

**示例**：
```
复合条件查询 (留空跳过该条件):
UID: 
手机号: 
姓名关键字: 张
性别 (M/F): M
工作城市: 北京
工作地点关键字: 
邮箱: 
页码 (默认1): 1
每页大小 (默认10): 10

查询条件: 复合条件查询: 姓名包含=张, 性别=M, 工作城市=北京
```

### 6. 全文搜索

**使用场景**：不确定关键字在哪个字段，需要全局搜索

**操作步骤**：
1. 选择菜单选项 `6`
2. 输入搜索关键字
3. 设置分页参数
4. 查看查询结果

**搜索范围**：所有文本字段，包括UID、手机号、姓名、邮箱、工作信息等

## 📄 分页功能

### 分页操作

当查询结果超过一页时，系统会提供分页选项：

```
分页选项:
p - 上一页
n - 下一页
g - 跳转到指定页
q - 退出分页
请选择: 
```

### 分页命令

- `p` - 上一页
- `n` - 下一页  
- `g` - 跳转到指定页码
- `q` - 退出分页，返回主菜单

### 分页信息显示

```
当前页: 2/10
显示记录: 11-20
总记录数: 95
```

## 🎨 高亮显示

查询结果中匹配的关键字会用 `<em>` 标签包围，在支持的终端中会高亮显示：

```
--- 记录 1 ---
姓名: 张三 (张 三)
匹配高亮:
  full_name: <em>张</em>三
  first_name: <em>张</em>
```

## ⚙️ 配置说明

### 必要配置

查询功能需要以下Elasticsearch配置：

```properties
# Elasticsearch连接配置
elasticsearch.host=localhost
elasticsearch.port=9200
elasticsearch.scheme=https
elasticsearch.username=elastic
elasticsearch.password=your_password
elasticsearch.index.name=user_data

# SSL配置（如果使用HTTPS）
elasticsearch.ssl.skip-verification=true
```

### 性能配置

```properties
# 查询超时时间（秒）
elasticsearch.query.timeout=30

# 默认分页大小
elasticsearch.query.default-page-size=10

# 最大分页大小
elasticsearch.query.max-page-size=100
```

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
```
错误: 查询执行失败: Connection refused
```

**解决方案**：
- 检查Elasticsearch服务是否运行
- 验证连接配置（主机、端口、认证信息）
- 检查SSL配置

#### 2. 索引不存在
```
错误: index_not_found_exception
```

**解决方案**：
- 确保已完成数据迁移
- 检查索引名称配置是否正确
- 验证索引是否存在：`curl -X GET "localhost:9200/_cat/indices"`

#### 3. 查询超时
```
错误: query timeout
```

**解决方案**：
- 增加查询超时时间
- 减少查询范围
- 优化查询条件

#### 4. 内存不足
```
错误: OutOfMemoryError
```

**解决方案**：
- 减少分页大小
- 增加JVM堆内存：`-Xmx2g`
- 优化查询条件

### 调试模式

启用调试日志：

```bash
java -Dlogging.level.com.example.migration=DEBUG -jar migration-tool.jar --query
```

## 📊 性能优化

### 查询性能建议

1. **精确查询优先**：UID和手机号查询性能最好
2. **合理分页**：避免一次查询过多数据
3. **优化查询条件**：使用更具体的查询条件
4. **避免通配符开头**：如`*keyword`性能较差

### 索引优化

如果查询性能不佳，可以考虑：

1. **添加索引**：为常用查询字段添加索引
2. **调整分片**：根据数据量调整分片数量
3. **优化映射**：使用合适的字段类型和分析器

## 🎯 最佳实践

1. **先精确后模糊**：优先使用UID、手机号等精确查询
2. **合理使用分页**：避免查询过多数据
3. **组合查询条件**：使用复合条件缩小查询范围
4. **关注查询耗时**：监控查询性能
5. **定期维护索引**：保持索引健康状态

## 📞 技术支持

如果遇到问题：

1. 查看日志文件：`logs/migration.log`
2. 检查配置文件：`application.properties`
3. 参考故障排除指南
4. 联系技术支持团队

---

通过以上功能，您可以方便地查询和浏览已迁移到Elasticsearch中的用户数据。查询工具提供了丰富的查询选项和友好的交互界面，满足各种数据查询需求。
