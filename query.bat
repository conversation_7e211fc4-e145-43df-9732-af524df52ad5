@echo off
REM Elasticsearch查询工具启动脚本 (Windows)

echo ========================================
echo Elasticsearch查询工具
echo 版本: 1.0.0
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java运行环境
    echo 请确保已安装Java 8或更高版本，并配置了JAVA_HOME环境变量
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "src\main\resources\application.properties" (
    echo 警告: 未找到配置文件 src\main\resources\application.properties
    if exist "application.properties" (
        echo 使用当前目录下的配置文件
    ) else (
        echo 错误: 未找到配置文件，请确保配置文件存在
        pause
        exit /b 1
    )
)

REM 检查JAR文件
set JAR_FILE=target\mysql-to-elasticsearch-migration-1.0.0.jar
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件 %JAR_FILE%
    echo 请先运行 mvn clean package 编译项目
    pause
    exit /b 1
)

REM 创建日志目录
if not exist "logs" mkdir logs

REM 设置JVM参数
set JAVA_OPTS=-Xms512m -Xmx1g -XX:+UseG1GC

echo 正在启动Elasticsearch查询工具...
echo JAR文件: %JAR_FILE%
echo JVM参数: %JAVA_OPTS%
echo.

REM 启动查询模式
java %JAVA_OPTS% -jar "%JAR_FILE%" --query

REM 检查执行结果
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 查询工具正常退出
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 查询工具异常退出，错误代码: %errorlevel%
    echo 请检查日志文件获取详细错误信息
    echo ========================================
)

echo.
echo 按任意键退出...
pause >nul
