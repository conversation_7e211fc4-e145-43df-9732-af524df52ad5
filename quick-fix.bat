@echo off
REM 快速修复SSL连接问题脚本

echo ========================================
echo SSL连接问题快速修复工具
echo ========================================
echo.

echo 正在检查配置文件...
if not exist "src\main\resources\application.properties" (
    echo 错误: 未找到配置文件
    pause
    exit /b 1
)

echo 当前Elasticsearch配置:
findstr "elasticsearch" src\main\resources\application.properties
echo.

echo 检测到HTTPS连接配置，这可能导致SSL证书验证失败。
echo.
echo 请选择解决方案:
echo 1. 跳过SSL验证（开发环境推荐）
echo 2. 改用HTTP连接（最简单）
echo 3. 手动配置SSL证书（生产环境）
echo 4. 测试当前连接
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto skip_ssl
if "%choice%"=="2" goto use_http
if "%choice%"=="3" goto manual_ssl
if "%choice%"=="4" goto test_connection
if "%choice%"=="5" goto exit
goto invalid_choice

:skip_ssl
echo.
echo 正在配置跳过SSL验证...
powershell -Command "(Get-Content 'src\main\resources\application.properties') -replace 'elasticsearch.ssl.skip-verification=false', 'elasticsearch.ssl.skip-verification=true' | Set-Content 'src\main\resources\application.properties'"
echo ✅ 已配置跳过SSL验证
echo.
echo ⚠️ 警告: 此配置仅适用于开发环境！
echo 生产环境请使用正确的SSL证书配置。
goto compile_and_test

:use_http
echo.
echo 正在配置HTTP连接...
powershell -Command "(Get-Content 'src\main\resources\application.properties') -replace 'elasticsearch.scheme=https', 'elasticsearch.scheme=http' | Set-Content 'src\main\resources\application.properties'"
powershell -Command "(Get-Content 'src\main\resources\application.properties') -replace 'elasticsearch.port=9200', 'elasticsearch.port=9200' | Set-Content 'src\main\resources\application.properties'"
echo ✅ 已配置HTTP连接
echo.
echo ℹ️ 注意: 确保Elasticsearch服务器支持HTTP连接
goto compile_and_test

:manual_ssl
echo.
echo 手动SSL配置说明:
echo 1. 获取Elasticsearch的CA证书
echo 2. 将证书保存为PEM格式
echo 3. 在配置文件中设置证书路径
echo 4. 详细步骤请参考 SSL_CONFIGURATION_GUIDE.md
echo.
pause
goto menu

:test_connection
echo.
echo 正在编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    goto menu
)

echo 正在测试连接...
java -cp "target/classes;target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester
pause
goto menu

:compile_and_test
echo.
echo 正在重新编译项目...
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    goto menu
)

echo ✅ 编译成功
echo.
echo 正在测试连接...
java -cp "target/classes;target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester

echo.
echo 如果连接测试成功，现在可以运行完整的数据迁移:
echo java -jar target\mysql-to-elasticsearch-migration-1.0.0.jar
echo.
pause
goto exit

:invalid_choice
echo 无效选择，请重新输入
goto menu

:menu
echo.
goto choice

:exit
echo 退出修复工具
exit /b 0
