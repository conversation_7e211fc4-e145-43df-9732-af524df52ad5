package com.example.migration.util;

import com.example.migration.config.MigrationConfig;
import com.example.migration.database.DatabaseManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 数据库连接诊断工具
 * 用于诊断和测试MySQL连接问题
 */
public class ConnectionDiagnosticTool {
    
    private static final Logger logger = LoggerFactory.getLogger(ConnectionDiagnosticTool.class);
    
    public static void main(String[] args) {
        logger.info("=== MySQL连接诊断工具 ===");
        
        try {
            MigrationConfig config = new MigrationConfig();
            DatabaseManager dbManager = new DatabaseManager(config);
            
            // 1. 基本连接测试
            testBasicConnection(dbManager);
            
            // 2. 连接池压力测试
            testConnectionPoolStress(dbManager, config);
            
            // 3. 长时间连接测试
            testLongRunningConnection(dbManager);
            
            // 4. 并发连接测试
            testConcurrentConnections(dbManager, config);
            
            // 5. 连接恢复测试
            testConnectionRecovery(dbManager);
            
            dbManager.close();
            logger.info("=== 诊断完成 ===");
            
        } catch (Exception e) {
            logger.error("诊断过程中发生错误", e);
        }
    }
    
    /**
     * 基本连接测试
     */
    private static void testBasicConnection(DatabaseManager dbManager) {
        logger.info("\n=== 1. 基本连接测试 ===");
        
        try (Connection conn = dbManager.getConnection()) {
            logger.info("✅ 基本连接成功");
            
            // 测试连接有效性
            if (conn.isValid(5)) {
                logger.info("✅ 连接有效性检查通过");
            } else {
                logger.warn("⚠️ 连接有效性检查失败");
            }
            
            // 测试简单查询
            try (PreparedStatement stmt = conn.prepareStatement("SELECT 1");
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    logger.info("✅ 简单查询测试通过");
                }
            }
            
            // 显示连接信息
            logger.info("数据库URL: {}", conn.getMetaData().getURL());
            logger.info("数据库产品: {}", conn.getMetaData().getDatabaseProductName());
            logger.info("数据库版本: {}", conn.getMetaData().getDatabaseProductVersion());
            logger.info("驱动版本: {}", conn.getMetaData().getDriverVersion());
            
        } catch (SQLException e) {
            logger.error("❌ 基本连接测试失败", e);
        }
    }
    
    /**
     * 连接池压力测试
     */
    private static void testConnectionPoolStress(DatabaseManager dbManager, MigrationConfig config) {
        logger.info("\n=== 2. 连接池压力测试 ===");
        
        int maxPoolSize = config.getMaximumPoolSize();
        logger.info("连接池最大大小: {}", maxPoolSize);
        
        Connection[] connections = new Connection[maxPoolSize + 5]; // 超过最大值
        
        try {
            // 尝试获取超过最大值的连接
            for (int i = 0; i < connections.length; i++) {
                try {
                    connections[i] = dbManager.getConnectionDirect();
                    logger.info("成功获取连接 {}", i + 1);
                } catch (SQLException e) {
                    logger.warn("获取连接 {} 失败: {}", i + 1, e.getMessage());
                    break;
                }
            }
            
            // 显示连接池状态
            dbManager.printPoolStatus();
            
        } finally {
            // 关闭所有连接
            for (Connection conn : connections) {
                if (conn != null) {
                    try {
                        conn.close();
                    } catch (SQLException e) {
                        logger.warn("关闭连接时发生错误", e);
                    }
                }
            }
        }
        
        logger.info("✅ 连接池压力测试完成");
    }
    
    /**
     * 长时间连接测试
     */
    private static void testLongRunningConnection(DatabaseManager dbManager) {
        logger.info("\n=== 3. 长时间连接测试 ===");
        
        try (Connection conn = dbManager.getConnection()) {
            logger.info("开始长时间连接测试，持续60秒...");
            
            for (int i = 0; i < 12; i++) { // 每5秒测试一次，共60秒
                Thread.sleep(5000);
                
                if (conn.isValid(5)) {
                    logger.info("第 {} 次检查: 连接仍然有效", i + 1);
                } else {
                    logger.warn("第 {} 次检查: 连接已失效", i + 1);
                    break;
                }
                
                // 执行简单查询
                try (PreparedStatement stmt = conn.prepareStatement("SELECT NOW()");
                     ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        logger.info("查询结果: {}", rs.getString(1));
                    }
                }
            }
            
            logger.info("✅ 长时间连接测试完成");
            
        } catch (Exception e) {
            logger.error("❌ 长时间连接测试失败", e);
        }
    }
    
    /**
     * 并发连接测试
     */
    private static void testConcurrentConnections(DatabaseManager dbManager, MigrationConfig config) {
        logger.info("\n=== 4. 并发连接测试 ===");
        
        int threadCount = config.getThreadPoolSize();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        try {
            Future<?>[] futures = new Future[threadCount];
            
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                futures[i] = executor.submit(() -> {
                    try {
                        for (int j = 0; j < 10; j++) {
                            try (Connection conn = dbManager.getConnection()) {
                                // 执行查询
                                try (PreparedStatement stmt = conn.prepareStatement("SELECT ?, ?");) {
                                    stmt.setInt(1, threadId);
                                    stmt.setInt(2, j);
                                    try (ResultSet rs = stmt.executeQuery()) {
                                        if (rs.next()) {
                                            logger.debug("线程 {} 第 {} 次查询成功", threadId, j + 1);
                                        }
                                    }
                                }
                                
                                // 模拟处理时间
                                Thread.sleep(100);
                            }
                        }
                        logger.info("线程 {} 完成所有查询", threadId);
                    } catch (Exception e) {
                        logger.error("线程 {} 执行失败", threadId, e);
                    }
                });
            }
            
            // 等待所有任务完成
            for (Future<?> future : futures) {
                future.get(30, TimeUnit.SECONDS);
            }
            
            logger.info("✅ 并发连接测试完成");
            
        } catch (Exception e) {
            logger.error("❌ 并发连接测试失败", e);
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 连接恢复测试
     */
    private static void testConnectionRecovery(DatabaseManager dbManager) {
        logger.info("\n=== 5. 连接恢复测试 ===");
        
        try {
            // 测试连接重试机制
            logger.info("测试连接重试机制...");
            
            for (int i = 0; i < 5; i++) {
                try (Connection conn = dbManager.getConnection()) {
                    logger.info("第 {} 次连接获取成功", i + 1);
                    
                    // 执行查询
                    try (PreparedStatement stmt = conn.prepareStatement("SELECT CONNECTION_ID()");
                         ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            logger.info("连接ID: {}", rs.getLong(1));
                        }
                    }
                    
                } catch (SQLException e) {
                    logger.warn("第 {} 次连接获取失败: {}", i + 1, e.getMessage());
                }
                
                Thread.sleep(1000);
            }
            
            // 测试连接池刷新
            logger.info("测试连接池刷新...");
            dbManager.refreshPool();
            
            // 刷新后再次测试
            try (Connection conn = dbManager.getConnection()) {
                logger.info("✅ 连接池刷新后连接成功");
            }
            
            logger.info("✅ 连接恢复测试完成");
            
        } catch (Exception e) {
            logger.error("❌ 连接恢复测试失败", e);
        }
    }
}
