package com.example.migration;

import com.example.migration.config.MigrationConfig;
import com.example.migration.model.UserData;
import org.junit.Test;

import java.time.LocalDate;

import static org.junit.Assert.*;

/**
 * 迁移应用程序测试类
 */
public class MigrationApplicationTest {
    
    @Test
    public void testMigrationConfigLoad() {
        try {
            MigrationConfig config = new MigrationConfig();
            assertNotNull("配置对象不应为空", config);
            
            // 测试默认值
            assertEquals("默认批处理大小应为1000", 1000, config.getBatchSize());
            assertEquals("默认线程池大小应为10", 10, config.getThreadPoolSize());
            assertEquals("默认表数量应为1", 1, config.getTableCount());
            assertEquals("默认表前缀应为data_", "data_", config.getTablePrefix());
            
        } catch (Exception e) {
            // 配置文件可能不存在，这在测试环境中是正常的
            System.out.println("配置文件加载失败（测试环境中正常）: " + e.getMessage());
        }
    }
    
    @Test
    public void testUserDataModel() {
        UserData userData = new UserData();
        
        // 设置测试数据
        userData.setMysqlId(12345);
        userData.setMysqlTable("data_0");
        userData.setUid("user123");
        userData.setPhoneNumber("13800138000");
        userData.setFirstName("张");
        userData.setLastName("三");
        userData.setGender("M");
        userData.setBirthDate(LocalDate.of(1990, 1, 1));
        userData.setBirthPlace("北京");
        userData.setWorkplaceCity("上海");
        userData.setMaritalStatus("单身");
        userData.setWorkPlace("某某公司");
        userData.setJoinDate(LocalDate.of(2020, 1, 1));
        userData.setEmail("<EMAIL>");
        
        // 生成计算字段
        userData.generateFullName();
        userData.generateNameInitials();
        
        // 验证结果
        assertEquals("全名应为'张 三'", "张 三", userData.getFullName());
        assertEquals("姓名首字母应为'ZS'", "ZS", userData.getNameInitials());
        assertEquals("文档ID应为'data_0_12345'", "data_0_12345", userData.generateDocumentId());
        
        // 验证基本字段
        assertEquals("MySQL ID应为12345", Integer.valueOf(12345), userData.getMysqlId());
        assertEquals("MySQL表名应为'data_0'", "data_0", userData.getMysqlTable());
        assertEquals("UID应为'user123'", "user123", userData.getUid());
        assertEquals("手机号应为'13800138000'", "13800138000", userData.getPhoneNumber());
        assertEquals("性别应为'M'", "M", userData.getGender());
        assertEquals("邮箱应为'<EMAIL>'", "<EMAIL>", userData.getEmail());
    }
    
    @Test
    public void testUserDataWithEmptyNames() {
        UserData userData = new UserData();
        
        // 测试空名字的情况
        userData.setFirstName("");
        userData.setLastName("");
        userData.generateFullName();
        userData.generateNameInitials();
        
        assertEquals("空名字的全名应为空字符串", "", userData.getFullName());
        assertEquals("空名字的首字母应为空字符串", "", userData.getNameInitials());
        
        // 测试只有名字的情况
        userData.setFirstName("张");
        userData.setLastName("");
        userData.generateFullName();
        userData.generateNameInitials();
        
        assertEquals("只有名字的全名应为'张'", "张", userData.getFullName());
        assertEquals("只有名字的首字母应为'Z'", "Z", userData.getNameInitials());
        
        // 测试只有姓氏的情况
        userData.setFirstName("");
        userData.setLastName("三");
        userData.generateFullName();
        userData.generateNameInitials();
        
        assertEquals("只有姓氏的全名应为'三'", "三", userData.getFullName());
        assertEquals("只有姓氏的首字母应为'S'", "S", userData.getNameInitials());
    }
    
    @Test
    public void testUserDataWithNullValues() {
        UserData userData = new UserData();
        
        // 测试null值的情况
        userData.setFirstName(null);
        userData.setLastName(null);
        userData.generateFullName();
        userData.generateNameInitials();
        
        assertEquals("null名字的全名应为空字符串", "", userData.getFullName());
        assertEquals("null名字的首字母应为空字符串", "", userData.getNameInitials());
    }
    
    @Test
    public void testDocumentIdGeneration() {
        UserData userData = new UserData();
        userData.setMysqlTable("data_99");
        userData.setMysqlId(999999);
        
        String documentId = userData.generateDocumentId();
        assertEquals("文档ID格式应正确", "data_99_999999", documentId);
        
        // 测试边界情况
        userData.setMysqlTable("data_0");
        userData.setMysqlId(1);
        documentId = userData.generateDocumentId();
        assertEquals("最小文档ID格式应正确", "data_0_1", documentId);
    }
    
    @Test
    public void testToString() {
        UserData userData = new UserData();
        userData.setMysqlId(123);
        userData.setMysqlTable("data_1");
        userData.setUid("test123");
        userData.setPhoneNumber("13800138000");
        userData.setFirstName("测试");
        userData.setLastName("用户");
        userData.setGender("F");
        userData.setBirthDate(LocalDate.of(1995, 5, 15));
        userData.setEmail("<EMAIL>");
        
        String toString = userData.toString();
        assertNotNull("toString不应为null", toString);
        assertTrue("toString应包含MySQL ID", toString.contains("123"));
        assertTrue("toString应包含表名", toString.contains("data_1"));
        assertTrue("toString应包含UID", toString.contains("test123"));
        assertTrue("toString应包含手机号", toString.contains("13800138000"));
    }
}
