package com.example.migration.util;

import com.example.migration.config.MigrationConfig;
import com.example.migration.model.QueryResult;
import com.example.migration.service.ElasticsearchQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询功能测试工具
 * 用于测试和验证Elasticsearch查询功能
 */
public class QueryFunctionTester {
    
    private static final Logger logger = LoggerFactory.getLogger(QueryFunctionTester.class);
    
    public static void main(String[] args) {
        logger.info("=== Elasticsearch查询功能测试工具 ===");
        
        try {
            MigrationConfig config = new MigrationConfig();
            ElasticsearchQueryService queryService = new ElasticsearchQueryService(config);
            
            // 测试各种查询功能
            testAllQueryFunctions(queryService);
            
            queryService.close();
            logger.info("=== 查询功能测试完成 ===");
            
        } catch (Exception e) {
            logger.error("查询功能测试失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 测试所有查询功能
     */
    private static void testAllQueryFunctions(ElasticsearchQueryService queryService) {
        logger.info("开始测试所有查询功能...");
        
        // 1. 测试UID查询
        testUidQuery(queryService);
        
        // 2. 测试手机号查询
        testPhoneNumberQuery(queryService);
        
        // 3. 测试姓名模糊查询
        testNameQuery(queryService);
        
        // 4. 测试工作地点查询
        testWorkplaceQuery(queryService);
        
        // 5. 测试复合条件查询
        testMultipleConditionsQuery(queryService);
        
        // 6. 测试全文搜索
        testFullTextSearch(queryService);
        
        // 7. 测试分页功能
        testPaginationQuery(queryService);
        
        // 8. 测试错误处理
        testErrorHandling(queryService);
    }
    
    /**
     * 测试UID查询
     */
    private static void testUidQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试UID查询 ===");
        
        try {
            // 测试存在的UID
            QueryResult result = queryService.findByUid("test_uid_001");
            printTestResult("UID查询 - 存在的UID", result);
            
            // 测试不存在的UID
            result = queryService.findByUid("non_existent_uid");
            printTestResult("UID查询 - 不存在的UID", result);
            
        } catch (Exception e) {
            logger.error("UID查询测试失败", e);
        }
    }
    
    /**
     * 测试手机号查询
     */
    private static void testPhoneNumberQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试手机号查询 ===");
        
        try {
            // 测试存在的手机号
            QueryResult result = queryService.findByPhoneNumber("13800138000");
            printTestResult("手机号查询 - 存在的手机号", result);
            
            // 测试不存在的手机号
            result = queryService.findByPhoneNumber("99999999999");
            printTestResult("手机号查询 - 不存在的手机号", result);
            
        } catch (Exception e) {
            logger.error("手机号查询测试失败", e);
        }
    }
    
    /**
     * 测试姓名模糊查询
     */
    private static void testNameQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试姓名模糊查询 ===");
        
        try {
            // 测试常见姓氏
            QueryResult result = queryService.findByName("张", 1, 5);
            printTestResult("姓名查询 - 姓氏'张'", result);
            
            // 测试常见名字
            result = queryService.findByName("明", 1, 5);
            printTestResult("姓名查询 - 名字'明'", result);
            
            // 测试全名
            result = queryService.findByName("张三", 1, 5);
            printTestResult("姓名查询 - 全名'张三'", result);
            
            // 测试英文名字
            result = queryService.findByName("John", 1, 5);
            printTestResult("姓名查询 - 英文名'John'", result);
            
        } catch (Exception e) {
            logger.error("姓名查询测试失败", e);
        }
    }
    
    /**
     * 测试工作地点查询
     */
    private static void testWorkplaceQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试工作地点查询 ===");
        
        try {
            // 测试城市查询
            QueryResult result = queryService.findByWorkplace("北京", 1, 5);
            printTestResult("工作地点查询 - 城市'北京'", result);
            
            // 测试公司查询
            result = queryService.findByWorkplace("阿里巴巴", 1, 5);
            printTestResult("工作地点查询 - 公司'阿里巴巴'", result);
            
            // 测试行业关键字
            result = queryService.findByWorkplace("互联网", 1, 5);
            printTestResult("工作地点查询 - 行业'互联网'", result);
            
        } catch (Exception e) {
            logger.error("工作地点查询测试失败", e);
        }
    }
    
    /**
     * 测试复合条件查询
     */
    private static void testMultipleConditionsQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试复合条件查询 ===");
        
        try {
            // 测试姓名+性别组合
            Map<String, String> conditions1 = new HashMap<>();
            conditions1.put("name", "张");
            conditions1.put("gender", "M");
            QueryResult result = queryService.findByMultipleConditions(conditions1, 1, 5);
            printTestResult("复合查询 - 姓名'张'+性别'M'", result);
            
            // 测试工作城市+性别组合
            Map<String, String> conditions2 = new HashMap<>();
            conditions2.put("workplace_city", "北京");
            conditions2.put("gender", "F");
            result = queryService.findByMultipleConditions(conditions2, 1, 5);
            printTestResult("复合查询 - 工作城市'北京'+性别'F'", result);
            
            // 测试多个条件组合
            Map<String, String> conditions3 = new HashMap<>();
            conditions3.put("name", "李");
            conditions3.put("workplace_city", "上海");
            conditions3.put("gender", "M");
            result = queryService.findByMultipleConditions(conditions3, 1, 5);
            printTestResult("复合查询 - 姓名'李'+城市'上海'+性别'M'", result);
            
            // 测试空条件
            Map<String, String> conditions4 = new HashMap<>();
            result = queryService.findByMultipleConditions(conditions4, 1, 5);
            printTestResult("复合查询 - 空条件", result);
            
        } catch (Exception e) {
            logger.error("复合条件查询测试失败", e);
        }
    }
    
    /**
     * 测试全文搜索
     */
    private static void testFullTextSearch(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试全文搜索 ===");
        
        try {
            // 测试姓名搜索
            QueryResult result = queryService.searchAll("张三", 1, 5);
            printTestResult("全文搜索 - '张三'", result);
            
            // 测试手机号搜索
            result = queryService.searchAll("138", 1, 5);
            printTestResult("全文搜索 - '138'", result);
            
            // 测试邮箱搜索
            result = queryService.searchAll("@qq.com", 1, 5);
            printTestResult("全文搜索 - '@qq.com'", result);
            
            // 测试工作地点搜索
            result = queryService.searchAll("科技", 1, 5);
            printTestResult("全文搜索 - '科技'", result);
            
        } catch (Exception e) {
            logger.error("全文搜索测试失败", e);
        }
    }
    
    /**
     * 测试分页功能
     */
    private static void testPaginationQuery(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试分页功能 ===");
        
        try {
            // 测试第一页
            QueryResult result = queryService.findByName("张", 1, 3);
            printTestResult("分页测试 - 第1页，每页3条", result);
            
            // 测试第二页
            result = queryService.findByName("张", 2, 3);
            printTestResult("分页测试 - 第2页，每页3条", result);
            
            // 测试大页码
            result = queryService.findByName("张", 100, 10);
            printTestResult("分页测试 - 第100页，每页10条", result);
            
            // 测试不同页面大小
            result = queryService.findByName("李", 1, 1);
            printTestResult("分页测试 - 第1页，每页1条", result);
            
            result = queryService.findByName("李", 1, 20);
            printTestResult("分页测试 - 第1页，每页20条", result);
            
        } catch (Exception e) {
            logger.error("分页功能测试失败", e);
        }
    }
    
    /**
     * 测试错误处理
     */
    private static void testErrorHandling(ElasticsearchQueryService queryService) {
        logger.info("\n=== 测试错误处理 ===");
        
        try {
            // 测试空字符串查询
            QueryResult result = queryService.findByUid("");
            printTestResult("错误处理 - 空UID查询", result);
            
            // 测试null查询
            result = queryService.findByUid(null);
            printTestResult("错误处理 - null UID查询", result);
            
            // 测试特殊字符查询
            result = queryService.findByName("@#$%^&*()", 1, 5);
            printTestResult("错误处理 - 特殊字符查询", result);
            
            // 测试超长字符串查询
            String longString = "a".repeat(1000);
            result = queryService.findByName(longString, 1, 5);
            printTestResult("错误处理 - 超长字符串查询", result);
            
        } catch (Exception e) {
            logger.error("错误处理测试失败", e);
        }
    }
    
    /**
     * 打印测试结果
     */
    private static void printTestResult(String testName, QueryResult result) {
        logger.info("测试: {}", testName);
        logger.info("  总数: {}", result.getTotalHits());
        logger.info("  耗时: {}ms", result.getTookInMillis());
        logger.info("  超时: {}", result.isTimedOut());
        logger.info("  结果数: {}", result.getResultCount());
        logger.info("  查询描述: {}", result.getQueryDescription());
        
        if (result.getTotalHits() > 0 && !result.getData().isEmpty()) {
            logger.info("  第一条记录UID: {}", result.getData().get(0).getUid());
            logger.info("  第一条记录姓名: {}", result.getData().get(0).getFullName());
        }
        
        // 验证结果合理性
        if (result.getTookInMillis() > 5000) {
            logger.warn("  ⚠️ 查询耗时过长: {}ms", result.getTookInMillis());
        }
        
        if (result.isTimedOut()) {
            logger.warn("  ⚠️ 查询超时");
        }
        
        logger.info("  ✅ 测试完成\n");
    }
}
