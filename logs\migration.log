2025-07-29 11:18:49.276 [main] INFO  c.e.migration.MigrationApplication - === MySQL到Elasticsearch数据迁移工具 ===
2025-07-29 11:18:49.280 [main] INFO  c.e.migration.MigrationApplication - 版本: 1.0.0
2025-07-29 11:18:49.280 [main] INFO  c.e.migration.MigrationApplication - 作者: 数据迁移团队
2025-07-29 11:18:49.280 [main] INFO  c.e.migration.MigrationApplication - ========================================
2025-07-29 11:18:49.284 [main] INFO  c.e.migration.config.MigrationConfig - 成功加载配置文件: application.properties
2025-07-29 11:18:49.296 [main] INFO  c.e.m.database.DatabaseManager - 正在初始化数据库连接池...
2025-07-29 11:18:49.341 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Starting...
2025-07-29 11:18:50.470 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MigrationPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@3098cf3b
2025-07-29 11:18:50.474 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Start completed.
2025-07-29 11:18:50.482 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接测试成功
2025-07-29 11:18:50.625 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接池初始化成功
2025-07-29 11:18:50.628 [main] INFO  c.e.m.e.ElasticsearchManager - 正在初始化Elasticsearch客户端...
2025-07-29 11:18:50.711 [main] INFO  c.e.m.e.ElasticsearchManager - 已配置Elasticsearch认证信息
2025-07-29 11:18:53.303 [main] ERROR c.e.m.e.ElasticsearchManager - Elasticsearch客户端初始化失败
org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: org.apache.http.ConnectionClosedException: Connection is closed
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: java.util.concurrent.ExecutionException: org.apache.http.ConnectionClosedException: Connection is closed
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: org.apache.http.ConnectionClosedException: Connection is closed
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.endOfInput(HttpAsyncRequestExecutor.java:356)
	at org.apache.http.impl.nio.DefaultNHttpClientConnection.consumeInput(DefaultNHttpClientConnection.java:261)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:81)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:114)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-29 11:18:53.317 [main] ERROR c.e.migration.MigrationApplication - 数据迁移失败
java.lang.RuntimeException: Elasticsearch客户端初始化失败
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:92)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: org.apache.http.ConnectionClosedException: Connection is closed
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	... 3 common frames omitted
Caused by: java.util.concurrent.ExecutionException: org.apache.http.ConnectionClosedException: Connection is closed
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: org.apache.http.ConnectionClosedException: Connection is closed
	at org.apache.http.nio.protocol.HttpAsyncRequestExecutor.endOfInput(HttpAsyncRequestExecutor.java:356)
	at org.apache.http.impl.nio.DefaultNHttpClientConnection.consumeInput(DefaultNHttpClientConnection.java:261)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:81)
	at org.apache.http.impl.nio.client.InternalIODispatch.onInputReady(InternalIODispatch.java:39)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:114)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-29 11:21:10.120 [main] INFO  c.e.migration.MigrationApplication - === MySQL到Elasticsearch数据迁移工具 ===
2025-07-29 11:21:10.125 [main] INFO  c.e.migration.MigrationApplication - 版本: 1.0.0
2025-07-29 11:21:10.125 [main] INFO  c.e.migration.MigrationApplication - 作者: 数据迁移团队
2025-07-29 11:21:10.125 [main] INFO  c.e.migration.MigrationApplication - ========================================
2025-07-29 11:21:10.128 [main] INFO  c.e.migration.config.MigrationConfig - 成功加载配置文件: application.properties
2025-07-29 11:21:10.155 [main] INFO  c.e.m.database.DatabaseManager - 正在初始化数据库连接池...
2025-07-29 11:21:10.189 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Starting...
2025-07-29 11:21:11.839 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MigrationPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@e3b3b2f
2025-07-29 11:21:11.845 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Start completed.
2025-07-29 11:21:11.853 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接测试成功
2025-07-29 11:21:11.887 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接池初始化成功
2025-07-29 11:21:11.890 [main] INFO  c.e.m.e.ElasticsearchManager - 正在初始化Elasticsearch客户端...
2025-07-29 11:21:12.145 [main] INFO  c.e.m.e.ElasticsearchManager - 已配置Elasticsearch认证信息
2025-07-29 11:21:15.240 [main] ERROR c.e.m.e.ElasticsearchManager - Elasticsearch客户端初始化失败
org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
2025-07-29 11:21:15.243 [main] ERROR c.e.migration.MigrationApplication - 数据迁移失败
java.lang.RuntimeException: Elasticsearch客户端初始化失败
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:92)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	... 3 common frames omitted
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
2025-07-29 11:21:51.694 [main] INFO  c.e.migration.MigrationApplication - === MySQL到Elasticsearch数据迁移工具 ===
2025-07-29 11:21:51.701 [main] INFO  c.e.migration.MigrationApplication - 版本: 1.0.0
2025-07-29 11:21:51.701 [main] INFO  c.e.migration.MigrationApplication - 作者: 数据迁移团队
2025-07-29 11:21:51.702 [main] INFO  c.e.migration.MigrationApplication - ========================================
2025-07-29 11:21:51.704 [main] INFO  c.e.migration.config.MigrationConfig - 成功加载配置文件: application.properties
2025-07-29 11:21:51.717 [main] INFO  c.e.m.database.DatabaseManager - 正在初始化数据库连接池...
2025-07-29 11:21:51.781 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Starting...
2025-07-29 11:21:53.074 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MigrationPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@e3b3b2f
2025-07-29 11:21:53.083 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Start completed.
2025-07-29 11:21:53.092 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接测试成功
2025-07-29 11:21:53.122 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接池初始化成功
2025-07-29 11:21:53.140 [main] INFO  c.e.m.e.ElasticsearchManager - 正在初始化Elasticsearch客户端...
2025-07-29 11:21:53.354 [main] INFO  c.e.m.e.ElasticsearchManager - 已配置Elasticsearch认证信息
2025-07-29 11:22:35.006 [main] ERROR c.e.m.e.ElasticsearchManager - Elasticsearch客户端初始化失败
org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
2025-07-29 11:22:35.009 [main] ERROR c.e.migration.MigrationApplication - 数据迁移失败
java.lang.RuntimeException: Elasticsearch客户端初始化失败
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:92)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	... 3 common frames omitted
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
2025-07-29 11:23:19.486 [main] INFO  c.e.migration.MigrationApplication - === MySQL到Elasticsearch数据迁移工具 ===
2025-07-29 11:23:19.492 [main] INFO  c.e.migration.MigrationApplication - 版本: 1.0.0
2025-07-29 11:23:19.492 [main] INFO  c.e.migration.MigrationApplication - 作者: 数据迁移团队
2025-07-29 11:23:19.492 [main] INFO  c.e.migration.MigrationApplication - ========================================
2025-07-29 11:23:19.494 [main] INFO  c.e.migration.config.MigrationConfig - 成功加载配置文件: application.properties
2025-07-29 11:23:19.506 [main] INFO  c.e.m.database.DatabaseManager - 正在初始化数据库连接池...
2025-07-29 11:23:19.540 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Starting...
2025-07-29 11:23:20.228 [main] INFO  com.zaxxer.hikari.pool.HikariPool - MigrationPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@e3b3b2f
2025-07-29 11:23:20.235 [main] INFO  com.zaxxer.hikari.HikariDataSource - MigrationPool - Start completed.
2025-07-29 11:23:20.240 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接测试成功
2025-07-29 11:23:20.275 [main] INFO  c.e.m.database.DatabaseManager - 数据库连接池初始化成功
2025-07-29 11:23:20.283 [main] INFO  c.e.m.e.ElasticsearchManager - 正在初始化Elasticsearch客户端...
2025-07-29 11:23:20.405 [main] INFO  c.e.m.e.ElasticsearchManager - 已配置Elasticsearch认证信息
2025-07-29 11:24:17.567 [MigrationPool housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - MigrationPool - Thread starvation or clock leap detected (housekeeper delta=57s217ms737µs700ns).
2025-07-29 11:24:17.582 [main] ERROR c.e.m.e.ElasticsearchManager - Elasticsearch客户端初始化失败
org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
2025-07-29 11:24:19.358 [main] ERROR c.e.migration.MigrationApplication - 数据迁移失败
java.lang.RuntimeException: Elasticsearch客户端初始化失败
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:92)
	at com.example.migration.elasticsearch.ElasticsearchManager.<init>(ElasticsearchManager.java:42)
	at com.example.migration.service.MigrationService.<init>(MigrationService.java:46)
	at com.example.migration.MigrationApplication.main(MigrationApplication.java:30)
Caused by: org.elasticsearch.ElasticsearchException: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2078)
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:1732)
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:1717)
	at org.elasticsearch.client.RestHighLevelClient.ping(RestHighLevelClient.java:815)
	at com.example.migration.elasticsearch.ElasticsearchManager.testConnection(ElasticsearchManager.java:101)
	at com.example.migration.elasticsearch.ElasticsearchManager.initializeClient(ElasticsearchManager.java:86)
	... 3 common frames omitted
Caused by: java.util.concurrent.ExecutionException: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.getValue(BaseFuture.java:262)
	at org.elasticsearch.common.util.concurrent.BaseFuture$Sync.get(BaseFuture.java:249)
	at org.elasticsearch.common.util.concurrent.BaseFuture.get(BaseFuture.java:76)
	at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2075)
	... 8 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.ssl.Alert.createSSLException(Alert.java:131)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:383)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:326)
	at java.base/sun.security.ssl.TransportContext.fatal(TransportContext.java:321)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1351)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.onConsumeCertificate(CertificateMessage.java:1226)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.consume(CertificateMessage.java:1169)
	at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:396)
	at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:480)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1277)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask$DelegatedAction.run(SSLEngineImpl.java:1264)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.base/sun.security.ssl.SSLEngineImpl$DelegatedTask.run(SSLEngineImpl.java:1209)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doRunTask(SSLIOSession.java:285)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.doHandshake(SSLIOSession.java:345)
	at org.apache.http.nio.reactor.ssl.SSLIOSession.isAppInputReady(SSLIOSession.java:523)
	at org.apache.http.impl.nio.reactor.AbstractIODispatch.inputReady(AbstractIODispatch.java:120)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.readable(BaseIOReactor.java:162)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvent(AbstractIOReactor.java:337)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.processEvents(AbstractIOReactor.java:315)
	at org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:276)
	at org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:439)
	at java.base/sun.security.validator.PKIXValidator.engineValidate(PKIXValidator.java:306)
	at java.base/sun.security.validator.Validator.validate(Validator.java:264)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkTrusted(X509TrustManagerImpl.java:285)
	at java.base/sun.security.ssl.X509TrustManagerImpl.checkServerTrusted(X509TrustManagerImpl.java:144)
	at java.base/sun.security.ssl.CertificateMessage$T13CertificateConsumer.checkServerCerts(CertificateMessage.java:1329)
	... 19 common frames omitted
Caused by: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.build(SunCertPathBuilder.java:148)
	at java.base/sun.security.provider.certpath.SunCertPathBuilder.engineBuild(SunCertPathBuilder.java:129)
	at java.base/java.security.cert.CertPathBuilder.build(CertPathBuilder.java:297)
	at java.base/sun.security.validator.PKIXValidator.doBuild(PKIXValidator.java:434)
	... 24 common frames omitted
