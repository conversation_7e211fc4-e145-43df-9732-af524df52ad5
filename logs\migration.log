2025-07-30 10:10:42.443 [main] INFO  c.e.m.util.QueryFunctionTester - === Elasticsearch查询功能测试工具 ===
2025-07-30 10:10:42.507 [main] INFO  c.e.migration.config.MigrationConfig - 成功加载配置文件: application.properties
2025-07-30 10:10:42.538 [main] INFO  c.e.m.e.ElasticsearchManager - 正在初始化Elasticsearch客户端...
2025-07-30 10:10:44.593 [main] INFO  c.e.m.e.ElasticsearchManager - Elasticsearch连接测试成功
2025-07-30 10:10:44.593 [main] INFO  c.e.m.e.ElasticsearchManager - Elasticsearch客户端初始化成功
2025-07-30 10:10:44.998 [main] INFO  c.e.m.util.QueryFunctionTester - 开始测试所有查询功能...
2025-07-30 10:10:44.999 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试UID查询 ===
2025-07-30 10:10:44.999 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据UID查询用户: test_uid_001
2025-07-30 10:10:45.348 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: UID精确查询: test_uid_001, 总数: 0, 耗时: 35ms
2025-07-30 10:10:45.349 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: UID查询 - 存在的UID
2025-07-30 10:10:45.349 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:45.349 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 35ms
2025-07-30 10:10:45.350 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:45.350 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:45.350 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: UID精确查询: test_uid_001
2025-07-30 10:10:45.350 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:45.350 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据UID查询用户: non_existent_uid
2025-07-30 10:10:45.363 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: UID精确查询: non_existent_uid, 总数: 0, 耗时: 2ms
2025-07-30 10:10:45.363 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: UID查询 - 不存在的UID
2025-07-30 10:10:45.363 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:45.364 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 2ms
2025-07-30 10:10:45.364 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:45.364 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:45.364 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: UID精确查询: non_existent_uid
2025-07-30 10:10:45.364 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:45.365 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试手机号查询 ===
2025-07-30 10:10:45.365 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据手机号查询用户: 13800138000
2025-07-30 10:10:45.386 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 手机号精确查询: 13800138000, 总数: 0, 耗时: 9ms
2025-07-30 10:10:45.387 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 手机号查询 - 存在的手机号
2025-07-30 10:10:45.387 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:45.387 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 9ms
2025-07-30 10:10:45.387 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:45.388 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:45.388 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 手机号精确查询: 13800138000
2025-07-30 10:10:45.388 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:45.389 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据手机号查询用户: 99999999999
2025-07-30 10:10:45.399 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 手机号精确查询: 99999999999, 总数: 0, 耗时: 1ms
2025-07-30 10:10:45.399 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 手机号查询 - 不存在的手机号
2025-07-30 10:10:45.400 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:45.400 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 1ms
2025-07-30 10:10:45.400 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:45.400 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:45.400 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 手机号精确查询: 99999999999
2025-07-30 10:10:45.401 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:45.402 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试姓名模糊查询 ===
2025-07-30 10:10:45.402 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 张, 页码: 1, 大小: 5
2025-07-30 10:10:47.085 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 张, 总数: 206, 耗时: 1029ms
2025-07-30 10:10:47.086 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 姓名查询 - 姓氏'张'
2025-07-30 10:10:47.087 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 206
2025-07-30 10:10:47.088 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 1029ms
2025-07-30 10:10:47.089 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:47.089 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:47.091 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 张
2025-07-30 10:10:47.091 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100014971962354
2025-07-30 10:10:47.092 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 张健 张
2025-07-30 10:10:47.092 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:47.092 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 明, 页码: 1, 大小: 5
2025-07-30 10:10:47.523 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 明, 总数: 181, 耗时: 407ms
2025-07-30 10:10:47.523 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 姓名查询 - 名字'明'
2025-07-30 10:10:47.524 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 181
2025-07-30 10:10:47.524 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 407ms
2025-07-30 10:10:47.525 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:47.525 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:47.525 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 明
2025-07-30 10:10:47.527 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100027133905004
2025-07-30 10:10:47.529 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 梦 明
2025-07-30 10:10:47.529 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:47.529 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 张三, 页码: 1, 大小: 5
2025-07-30 10:10:47.738 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 张三, 总数: 212, 耗时: 180ms
2025-07-30 10:10:47.739 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 姓名查询 - 全名'张三'
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 212
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 180ms
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 张三
2025-07-30 10:10:47.740 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100011334563570
2025-07-30 10:10:47.741 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 三 张
2025-07-30 10:10:47.741 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:47.741 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: John, 页码: 1, 大小: 5
2025-07-30 10:10:48.180 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: John, 总数: 6664, 耗时: 397ms
2025-07-30 10:10:48.184 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 姓名查询 - 英文名'John'
2025-07-30 10:10:48.184 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 6664
2025-07-30 10:10:48.184 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 397ms
2025-07-30 10:10:48.185 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.185 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.185 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: John
2025-07-30 10:10:48.185 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100026776124007
2025-07-30 10:10:48.185 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: John John John John
2025-07-30 10:10:48.186 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.186 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试工作地点查询 ===
2025-07-30 10:10:48.186 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据工作地点查询: 北京, 页码: 1, 大小: 5
2025-07-30 10:10:48.292 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 工作地点查询: 北京, 总数: 94, 耗时: 93ms
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 工作地点查询 - 城市'北京'
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 94
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 93ms
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 工作地点查询: 北京
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100031809360772
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 子平 王
2025-07-30 10:10:48.293 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.294 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据工作地点查询: 阿里巴巴, 页码: 1, 大小: 5
2025-07-30 10:10:48.406 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 工作地点查询: 阿里巴巴, 总数: 33, 耗时: 96ms
2025-07-30 10:10:48.406 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 工作地点查询 - 公司'阿里巴巴'
2025-07-30 10:10:48.406 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 33
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 96ms
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 工作地点查询: 阿里巴巴
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100011491544751
2025-07-30 10:10:48.407 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: Lebekan Arionk
2025-07-30 10:10:48.408 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.409 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据工作地点查询: 互联网, 页码: 1, 大小: 5
2025-07-30 10:10:48.506 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 工作地点查询: 互联网, 总数: 19, 耗时: 79ms
2025-07-30 10:10:48.506 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 工作地点查询 - 行业'互联网'
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 19
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 79ms
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 工作地点查询: 互联网
2025-07-30 10:10:48.507 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100003350110824
2025-07-30 10:10:48.508 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 韻伊 覃
2025-07-30 10:10:48.508 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.508 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试复合条件查询 ===
2025-07-30 10:10:48.508 [main] INFO  c.e.m.s.ElasticsearchQueryService - 复合条件查询: {gender=M, name=张}, 页码: 1, 大小: 5
2025-07-30 10:10:48.524 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 复合条件查询: 性别=M, 姓名包含=张, 总数: 0, 耗时: 6ms
2025-07-30 10:10:48.525 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 复合查询 - 姓名'张'+性别'M'
2025-07-30 10:10:48.525 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:48.525 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 6ms
2025-07-30 10:10:48.525 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.526 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:48.526 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 复合条件查询: 性别=M, 姓名包含=张
2025-07-30 10:10:48.526 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.526 [main] INFO  c.e.m.s.ElasticsearchQueryService - 复合条件查询: {gender=F, workplace_city=北京}, 页码: 1, 大小: 5
2025-07-30 10:10:48.541 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 复合条件查询: 性别=F, 工作城市=北京, 总数: 0, 耗时: 5ms
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 复合查询 - 工作城市'北京'+性别'F'
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 5ms
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 复合条件查询: 性别=F, 工作城市=北京
2025-07-30 10:10:48.542 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.543 [main] INFO  c.e.m.s.ElasticsearchQueryService - 复合条件查询: {gender=M, name=李, workplace_city=上海}, 页码: 1, 大小: 5
2025-07-30 10:10:48.574 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 复合条件查询: 性别=M, 姓名包含=李, 工作城市=上海, 总数: 0, 耗时: 8ms
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 复合查询 - 姓名'李'+城市'上海'+性别'M'
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 8ms
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:48.575 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 复合条件查询: 性别=M, 姓名包含=李, 工作城市=上海
2025-07-30 10:10:48.576 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.576 [main] INFO  c.e.m.s.ElasticsearchQueryService - 复合条件查询: {}, 页码: 1, 大小: 5
2025-07-30 10:10:48.576 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 复合查询 - 空条件
2025-07-30 10:10:48.576 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:48.576 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 0ms
2025-07-30 10:10:48.577 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.577 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:48.577 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 无有效查询条件
2025-07-30 10:10:48.577 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.577 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试全文搜索 ===
2025-07-30 10:10:48.578 [main] INFO  c.e.m.s.ElasticsearchQueryService - 全文搜索: 张三, 页码: 1, 大小: 5
2025-07-30 10:10:48.671 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 全文搜索: 张三, 总数: 237, 耗时: 63ms
2025-07-30 10:10:48.671 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 全文搜索 - '张三'
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 237
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 63ms
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 全文搜索: 张三
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100011334563570
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 三 张
2025-07-30 10:10:48.672 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.672 [main] INFO  c.e.m.s.ElasticsearchQueryService - 全文搜索: 138, 页码: 1, 大小: 5
2025-07-30 10:10:48.707 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 全文搜索: 138, 总数: 2, 耗时: 23ms
2025-07-30 10:10:48.708 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 全文搜索 - '138'
2025-07-30 10:10:48.708 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 2
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 23ms
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 2
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 全文搜索: 138
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100000277090673
2025-07-30 10:10:48.709 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: Luis Leandro
2025-07-30 10:10:48.710 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.710 [main] INFO  c.e.m.s.ElasticsearchQueryService - 全文搜索: @qq.com, 页码: 1, 大小: 5
2025-07-30 10:10:48.746 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 全文搜索: @qq.com, 总数: 0, 耗时: 11ms
2025-07-30 10:10:48.746 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 全文搜索 - '@qq.com'
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 11ms
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 全文搜索: @qq.com
2025-07-30 10:10:48.747 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.748 [main] INFO  c.e.m.s.ElasticsearchQueryService - 全文搜索: 科技, 页码: 1, 大小: 5
2025-07-30 10:10:48.794 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 全文搜索: 科技, 总数: 95, 耗时: 30ms
2025-07-30 10:10:48.795 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 全文搜索 - '科技'
2025-07-30 10:10:48.795 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 95
2025-07-30 10:10:48.795 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 30ms
2025-07-30 10:10:48.796 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:48.796 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 5
2025-07-30 10:10:48.797 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 全文搜索: 科技
2025-07-30 10:10:48.797 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100007936061885
2025-07-30 10:10:48.797 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 科瑆 黄
2025-07-30 10:10:48.797 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:48.797 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试分页功能 ===
2025-07-30 10:10:48.797 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 张, 页码: 1, 大小: 3
2025-07-30 10:10:49.089 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 张, 总数: 206, 耗时: 278ms
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 分页测试 - 第1页，每页3条
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 206
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 278ms
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 3
2025-07-30 10:10:49.090 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 张
2025-07-30 10:10:49.091 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100014971962354
2025-07-30 10:10:49.091 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 张健 张
2025-07-30 10:10:49.091 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.091 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 张, 页码: 2, 大小: 3
2025-07-30 10:10:49.239 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 张, 总数: 206, 耗时: 136ms
2025-07-30 10:10:49.239 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 分页测试 - 第2页，每页3条
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 206
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 136ms
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 3
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 张
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100036077092181
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 昱 张
2025-07-30 10:10:49.240 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.240 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 张, 页码: 100, 大小: 10
2025-07-30 10:10:49.418 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 张, 总数: 206, 耗时: 169ms
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 分页测试 - 第100页，每页10条
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 206
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 169ms
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 张
2025-07-30 10:10:49.419 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.420 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 李, 页码: 1, 大小: 1
2025-07-30 10:10:49.540 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 李, 总数: 517, 耗时: 112ms
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 分页测试 - 第1页，每页1条
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 517
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 112ms
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 1
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 李
2025-07-30 10:10:49.540 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100013035227343
2025-07-30 10:10:49.541 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 李 李
2025-07-30 10:10:49.541 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.541 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据姓名模糊查询: 李, 页码: 1, 大小: 20
2025-07-30 10:10:49.725 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: 姓名模糊查询: 李, 总数: 517, 耗时: 161ms
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 分页测试 - 第1页，每页20条
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 517
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 161ms
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 20
2025-07-30 10:10:49.725 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: 姓名模糊查询: 李
2025-07-30 10:10:49.726 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录UID: 100013035227343
2025-07-30 10:10:49.726 [main] INFO  c.e.m.util.QueryFunctionTester -   第一条记录姓名: 李 李
2025-07-30 10:10:49.726 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.727 [main] INFO  c.e.m.util.QueryFunctionTester - 
=== 测试错误处理 ===
2025-07-30 10:10:49.727 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据UID查询用户: 
2025-07-30 10:10:49.739 [main] INFO  c.e.m.s.ElasticsearchQueryService - 查询完成: UID精确查询: , 总数: 0, 耗时: 2ms
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester - 测试: 错误处理 - 空UID查询
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   总数: 0
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   耗时: 2ms
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   超时: false
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   结果数: 0
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   查询描述: UID精确查询: 
2025-07-30 10:10:49.740 [main] INFO  c.e.m.util.QueryFunctionTester -   ✅ 测试完成

2025-07-30 10:10:49.740 [main] INFO  c.e.m.s.ElasticsearchQueryService - 根据UID查询用户: null
2025-07-30 10:10:49.745 [main] ERROR c.e.m.util.QueryFunctionTester - 错误处理测试失败
java.lang.IllegalArgumentException: value cannot be null
	at org.elasticsearch.index.query.BaseTermQueryBuilder.<init>(BaseTermQueryBuilder.java:105)
	at org.elasticsearch.index.query.TermQueryBuilder.<init>(TermQueryBuilder.java:44)
	at org.elasticsearch.index.query.QueryBuilders.termQuery(QueryBuilders.java:162)
	at com.example.migration.service.ElasticsearchQueryService.findByUid(ElasticsearchQueryService.java:57)
	at com.example.migration.util.QueryFunctionTester.testErrorHandling(QueryFunctionTester.java:272)
	at com.example.migration.util.QueryFunctionTester.testAllQueryFunctions(QueryFunctionTester.java:67)
	at com.example.migration.util.QueryFunctionTester.main(QueryFunctionTester.java:28)
2025-07-30 10:10:49.752 [main] INFO  c.e.m.e.ElasticsearchManager - 正在关闭Elasticsearch客户端...
2025-07-30 10:10:49.755 [main] INFO  c.e.m.e.ElasticsearchManager - Elasticsearch客户端已关闭
2025-07-30 10:10:49.757 [main] INFO  c.e.m.util.QueryFunctionTester - === 查询功能测试完成 ===
