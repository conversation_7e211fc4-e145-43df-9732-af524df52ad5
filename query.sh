#!/bin/bash

# Elasticsearch查询工具启动脚本 (Linux/Mac)

echo "========================================"
echo "Elasticsearch查询工具"
echo "版本: 1.0.0"
echo "========================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java运行环境"
    echo "请确保已安装Java 8或更高版本，并配置了PATH环境变量"
    exit 1
fi

# 显示Java版本
echo "Java版本信息:"
java -version
echo

# 检查配置文件
if [ ! -f "src/main/resources/application.properties" ]; then
    echo "警告: 未找到配置文件 src/main/resources/application.properties"
    if [ -f "application.properties" ]; then
        echo "使用当前目录下的配置文件"
    else
        echo "错误: 未找到配置文件，请确保配置文件存在"
        exit 1
    fi
fi

# 检查JAR文件
JAR_FILE="target/mysql-to-elasticsearch-migration-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: 未找到JAR文件 $JAR_FILE"
    echo "请先运行 mvn clean package 编译项目"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 设置JVM参数
JAVA_OPTS="-Xms512m -Xmx1g -XX:+UseG1GC"

echo "正在启动Elasticsearch查询工具..."
echo "JAR文件: $JAR_FILE"
echo "JVM参数: $JAVA_OPTS"
echo

# 启动查询模式
java $JAVA_OPTS -jar "$JAR_FILE" --query

# 检查执行结果
if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "查询工具正常退出"
    echo "========================================"
else
    echo
    echo "========================================"
    echo "查询工具异常退出，错误代码: $?"
    echo "请检查日志文件获取详细错误信息"
    echo "========================================"
    exit 1
fi
