package com.example.migration.util;

import com.example.migration.config.MigrationConfig;
import com.example.migration.model.QueryResult;
import com.example.migration.model.UserData;
import com.example.migration.service.ElasticsearchQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * 查询工具类
 * 提供交互式查询界面和结果格式化输出
 */
public class QueryTool {
    
    private static final Logger logger = LoggerFactory.getLogger(QueryTool.class);
    
    private ElasticsearchQueryService queryService;
    private Scanner scanner;
    
    public QueryTool(MigrationConfig config) {
        this.queryService = new ElasticsearchQueryService(config);
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * 启动交互式查询模式
     */
    public void startInteractiveMode() {
        System.out.println("========================================");
        System.out.println("Elasticsearch 交互式查询工具");
        System.out.println("========================================");
        System.out.println();
        
        while (true) {
            try {
                showMainMenu();
                String choice = scanner.nextLine().trim();
                
                switch (choice) {
                    case "1":
                        queryByUid();
                        break;
                    case "2":
                        queryByPhoneNumber();
                        break;
                    case "3":
                        queryByName();
                        break;
                    case "4":
                        queryByWorkplace();
                        break;
                    case "5":
                        queryByMultipleConditions();
                        break;
                    case "6":
                        searchAll();
                        break;
                    case "0":
                    case "exit":
                    case "quit":
                        System.out.println("退出查询工具");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入");
                        break;
                }
                
                System.out.println();
                System.out.print("按回车键继续...");
                scanner.nextLine();
                System.out.println();
                
            } catch (Exception e) {
                logger.error("查询过程中发生错误", e);
                System.err.println("查询失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 显示主菜单
     */
    private void showMainMenu() {
        System.out.println("请选择查询类型:");
        System.out.println("1. 根据UID查询");
        System.out.println("2. 根据手机号查询");
        System.out.println("3. 根据姓名模糊查询");
        System.out.println("4. 根据工作地点查询");
        System.out.println("5. 复合条件查询");
        System.out.println("6. 全文搜索");
        System.out.println("0. 退出");
        System.out.print("请输入选择 (0-6): ");
    }
    
    /**
     * 根据UID查询
     */
    private void queryByUid() {
        System.out.print("请输入UID: ");
        String uid = scanner.nextLine().trim();
        
        if (uid.isEmpty()) {
            System.out.println("UID不能为空");
            return;
        }
        
        QueryResult result = queryService.findByUid(uid);
        displayQueryResult(result);
    }
    
    /**
     * 根据手机号查询
     */
    private void queryByPhoneNumber() {
        System.out.print("请输入手机号: ");
        String phoneNumber = scanner.nextLine().trim();
        
        if (phoneNumber.isEmpty()) {
            System.out.println("手机号不能为空");
            return;
        }
        
        QueryResult result = queryService.findByPhoneNumber(phoneNumber);
        displayQueryResult(result);
    }
    
    /**
     * 根据姓名模糊查询
     */
    private void queryByName() {
        System.out.print("请输入姓名关键字: ");
        String name = scanner.nextLine().trim();
        
        if (name.isEmpty()) {
            System.out.println("姓名关键字不能为空");
            return;
        }
        
        int[] pageInfo = getPageInfo();
        QueryResult result = queryService.findByName(name, pageInfo[0], pageInfo[1]);
        displayQueryResult(result);
        
        // 支持翻页
        handlePagination(result, () -> queryService.findByName(name, result.getCurrentPage(), result.getPageSize()));
    }
    
    /**
     * 根据工作地点查询
     */
    private void queryByWorkplace() {
        System.out.print("请输入工作地点关键字: ");
        String workplace = scanner.nextLine().trim();
        
        if (workplace.isEmpty()) {
            System.out.println("工作地点关键字不能为空");
            return;
        }
        
        int[] pageInfo = getPageInfo();
        QueryResult result = queryService.findByWorkplace(workplace, pageInfo[0], pageInfo[1]);
        displayQueryResult(result);
        
        // 支持翻页
        handlePagination(result, () -> queryService.findByWorkplace(workplace, result.getCurrentPage(), result.getPageSize()));
    }
    
    /**
     * 复合条件查询
     */
    private void queryByMultipleConditions() {
        System.out.println("复合条件查询 (留空跳过该条件):");
        
        Map<String, String> conditions = new HashMap<>();
        
        System.out.print("UID: ");
        String uid = scanner.nextLine().trim();
        if (!uid.isEmpty()) conditions.put("uid", uid);
        
        System.out.print("手机号: ");
        String phoneNumber = scanner.nextLine().trim();
        if (!phoneNumber.isEmpty()) conditions.put("phone_number", phoneNumber);
        
        System.out.print("姓名关键字: ");
        String name = scanner.nextLine().trim();
        if (!name.isEmpty()) conditions.put("name", name);
        
        System.out.print("性别 (M/F): ");
        String gender = scanner.nextLine().trim();
        if (!gender.isEmpty()) conditions.put("gender", gender);
        
        System.out.print("工作城市: ");
        String workplaceCity = scanner.nextLine().trim();
        if (!workplaceCity.isEmpty()) conditions.put("workplace_city", workplaceCity);
        
        System.out.print("工作地点关键字: ");
        String workPlace = scanner.nextLine().trim();
        if (!workPlace.isEmpty()) conditions.put("work_place", workPlace);
        
        System.out.print("邮箱: ");
        String email = scanner.nextLine().trim();
        if (!email.isEmpty()) conditions.put("email", email);
        
        if (conditions.isEmpty()) {
            System.out.println("至少需要输入一个查询条件");
            return;
        }
        
        int[] pageInfo = getPageInfo();
        QueryResult result = queryService.findByMultipleConditions(conditions, pageInfo[0], pageInfo[1]);
        displayQueryResult(result);
        
        // 支持翻页
        handlePagination(result, () -> queryService.findByMultipleConditions(conditions, result.getCurrentPage(), result.getPageSize()));
    }
    
    /**
     * 全文搜索
     */
    private void searchAll() {
        System.out.print("请输入搜索关键字: ");
        String keyword = scanner.nextLine().trim();
        
        if (keyword.isEmpty()) {
            System.out.println("搜索关键字不能为空");
            return;
        }
        
        int[] pageInfo = getPageInfo();
        QueryResult result = queryService.searchAll(keyword, pageInfo[0], pageInfo[1]);
        displayQueryResult(result);
        
        // 支持翻页
        handlePagination(result, () -> queryService.searchAll(keyword, result.getCurrentPage(), result.getPageSize()));
    }
    
    /**
     * 获取分页信息
     */
    private int[] getPageInfo() {
        System.out.print("页码 (默认1): ");
        String pageStr = scanner.nextLine().trim();
        int page = pageStr.isEmpty() ? 1 : Integer.parseInt(pageStr);
        
        System.out.print("每页大小 (默认10): ");
        String sizeStr = scanner.nextLine().trim();
        int size = sizeStr.isEmpty() ? 10 : Integer.parseInt(sizeStr);
        
        return new int[]{page, size};
    }
    
    /**
     * 显示查询结果
     */
    private void displayQueryResult(QueryResult result) {
        System.out.println();
        System.out.println("========================================");
        System.out.println("查询结果");
        System.out.println("========================================");
        System.out.println("查询条件: " + result.getQueryDescription());
        System.out.println("总记录数: " + result.getTotalHits());
        System.out.println("查询耗时: " + result.getTookInMillis() + "ms");
        
        if (result.isEmpty()) {
            System.out.println("未找到匹配的记录");
            return;
        }
        
        System.out.println("当前页: " + result.getCurrentPage() + "/" + result.getTotalPages());
        System.out.println("显示记录: " + result.getStartRecord() + "-" + result.getEndRecord());
        System.out.println();
        
        // 显示数据
        List<UserData> data = result.getData();
        for (int i = 0; i < data.size(); i++) {
            UserData user = data.get(i);
            System.out.println("--- 记录 " + (i + 1) + " ---");
            System.out.println("UID: " + user.getUid());
            System.out.println("手机号: " + user.getPhoneNumber());
            System.out.println("姓名: " + user.getFullName() + " (" + user.getFirstName() + " " + user.getLastName() + ")");
            System.out.println("性别: " + user.getGender());
            System.out.println("邮箱: " + user.getEmail());
            System.out.println("出生地: " + user.getBirthPlace());
            System.out.println("工作城市: " + user.getWorkplaceCity());
            System.out.println("工作地点: " + user.getWorkPlace());
            System.out.println("MySQL来源: " + user.getMysqlTable() + " (ID: " + user.getMysqlId() + ")");
            
            // 显示高亮信息
            if (result.getHighlights() != null) {
                String docId = user.getMysqlTable() + "_" + user.getMysqlId();
                Map<String, List<String>> highlights = result.getHighlights().get(docId);
                if (highlights != null && !highlights.isEmpty()) {
                    System.out.println("匹配高亮:");
                    for (Map.Entry<String, List<String>> entry : highlights.entrySet()) {
                        System.out.println("  " + entry.getKey() + ": " + String.join(", ", entry.getValue()));
                    }
                }
            }
            System.out.println();
        }
    }
    
    /**
     * 处理分页
     */
    private void handlePagination(QueryResult result, PaginationCallback callback) {
        if (result.getTotalPages() <= 1) {
            return;
        }
        
        while (true) {
            System.out.println("分页选项:");
            if (result.hasPreviousPage()) {
                System.out.println("p - 上一页");
            }
            if (result.hasNextPage()) {
                System.out.println("n - 下一页");
            }
            System.out.println("g - 跳转到指定页");
            System.out.println("q - 退出分页");
            System.out.print("请选择: ");
            
            String choice = scanner.nextLine().trim().toLowerCase();
            
            switch (choice) {
                case "p":
                    if (result.hasPreviousPage()) {
                        result.setCurrentPage(result.getCurrentPage() - 1);
                        QueryResult newResult = callback.execute();
                        displayQueryResult(newResult);
                        result = newResult;
                    } else {
                        System.out.println("已经是第一页");
                    }
                    break;
                case "n":
                    if (result.hasNextPage()) {
                        result.setCurrentPage(result.getCurrentPage() + 1);
                        QueryResult newResult = callback.execute();
                        displayQueryResult(newResult);
                        result = newResult;
                    } else {
                        System.out.println("已经是最后一页");
                    }
                    break;
                case "g":
                    System.out.print("请输入页码 (1-" + result.getTotalPages() + "): ");
                    try {
                        int targetPage = Integer.parseInt(scanner.nextLine().trim());
                        if (targetPage >= 1 && targetPage <= result.getTotalPages()) {
                            result.setCurrentPage(targetPage);
                            QueryResult newResult = callback.execute();
                            displayQueryResult(newResult);
                            result = newResult;
                        } else {
                            System.out.println("页码超出范围");
                        }
                    } catch (NumberFormatException e) {
                        System.out.println("无效的页码");
                    }
                    break;
                case "q":
                    return;
                default:
                    System.out.println("无效选择");
                    break;
            }
        }
    }
    
    /**
     * 分页回调接口
     */
    @FunctionalInterface
    private interface PaginationCallback {
        QueryResult execute();
    }
    
    /**
     * 关闭工具
     */
    public void close() {
        if (queryService != null) {
            queryService.close();
        }
        if (scanner != null) {
            scanner.close();
        }
    }
}
