#!/bin/bash

# SSL连接问题快速修复工具

echo "========================================"
echo "SSL连接问题快速修复工具"
echo "========================================"
echo

# 检查配置文件
if [ ! -f "src/main/resources/application.properties" ]; then
    echo "错误: 未找到配置文件"
    exit 1
fi

echo "当前Elasticsearch配置:"
grep "elasticsearch" src/main/resources/application.properties
echo

echo "检测到HTTPS连接配置，这可能导致SSL证书验证失败。"
echo
echo "请选择解决方案:"
echo "1. 跳过SSL验证（开发环境推荐）"
echo "2. 改用HTTP连接（最简单）"
echo "3. 手动配置SSL证书（生产环境）"
echo "4. 测试当前连接"
echo "5. 退出"
echo

while true; do
    read -p "请输入选择 (1-5): " choice
    case $choice in
        1)
            echo
            echo "正在配置跳过SSL验证..."
            sed -i 's/elasticsearch.ssl.skip-verification=false/elasticsearch.ssl.skip-verification=true/g' src/main/resources/application.properties
            echo "✅ 已配置跳过SSL验证"
            echo
            echo "⚠️ 警告: 此配置仅适用于开发环境！"
            echo "生产环境请使用正确的SSL证书配置。"
            compile_and_test
            break
            ;;
        2)
            echo
            echo "正在配置HTTP连接..."
            sed -i 's/elasticsearch.scheme=https/elasticsearch.scheme=http/g' src/main/resources/application.properties
            echo "✅ 已配置HTTP连接"
            echo
            echo "ℹ️ 注意: 确保Elasticsearch服务器支持HTTP连接"
            compile_and_test
            break
            ;;
        3)
            echo
            echo "手动SSL配置说明:"
            echo "1. 获取Elasticsearch的CA证书"
            echo "2. 将证书保存为PEM格式"
            echo "3. 在配置文件中设置证书路径"
            echo "4. 详细步骤请参考 SSL_CONFIGURATION_GUIDE.md"
            echo
            read -p "按Enter键继续..."
            ;;
        4)
            echo
            echo "正在编译项目..."
            mvn clean compile -q
            if [ $? -ne 0 ]; then
                echo "编译失败，请检查代码"
                read -p "按Enter键继续..."
                continue
            fi
            
            echo "正在测试连接..."
            java -cp "target/classes:target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester
            read -p "按Enter键继续..."
            ;;
        5)
            echo "退出修复工具"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done

function compile_and_test() {
    echo
    echo "正在重新编译项目..."
    mvn clean package -DskipTests -q
    if [ $? -ne 0 ]; then
        echo "编译失败，请检查错误信息"
        read -p "按Enter键继续..."
        return
    fi
    
    echo "✅ 编译成功"
    echo
    echo "正在测试连接..."
    java -cp "target/classes:target/dependency/*" com.example.migration.util.ElasticsearchConnectionTester
    
    echo
    echo "如果连接测试成功，现在可以运行完整的数据迁移:"
    echo "java -jar target/mysql-to-elasticsearch-migration-1.0.0.jar"
    echo
    read -p "按Enter键退出..."
}
